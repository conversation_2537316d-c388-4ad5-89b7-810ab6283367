import { Atom, Cpu, Microscope, Zap } from 'lucide-react';
import React from 'react';
import { INNOVATION } from '../content';

const InnovationSection: React.FC = () => {
  const innovation = INNOVATION;
  const iconMap = {
    Atom,
    Cpu,
    Microscope,
    Zap,
  } as const;

  return (
    <section id="innovation" className="py-20 bg-black">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16 animate-on-scroll">
          <h2 className="text-4xl md:text-5xl lg:text-6xl font-thin text-white mb-6">
            {innovation.section.title}
            <span className="block metallic-gradient font-light">
              {innovation.section.highlight}
            </span>
          </h2>
          <p className="text-lg text-gray-400 max-w-3xl mx-auto leading-relaxed">
            {innovation.section.description}
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-20">
          {innovation.cards.map((item, index) => {
            const Icon = iconMap[item.icon as keyof typeof iconMap];
            return (
              <div
                key={index}
                className="text-center group animate-on-scroll"
                style={{ animationDelay: `${index * 0.2}s` }}>
                <div className="glass-effect rounded-2xl p-8 hover-lift">
                  <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-gradient-to-r from-gray-400 to-yellow-500 mb-6 group-hover:scale-110 transition-transform duration-300">
                    <Icon className="w-8 h-8 text-black" />
                  </div>

                  <div className="text-3xl font-light metallic-gradient mono-font mb-2">
                    {item.metric}
                  </div>
                  <div className="text-sm text-gray-500 uppercase tracking-wider mb-4">
                    {item.unit}
                  </div>
                  <h3 className="text-lg font-semibold text-white mb-4">{item.title}</h3>
                  <p className="text-gray-400 leading-relaxed text-sm">{item.description}</p>
                </div>
              </div>
            );
          })}
        </div>

        <div className="glass-effect rounded-3xl p-8 md:p-12 animate-on-scroll">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div>
              <h3 className="text-3xl font-light text-white mb-6">{innovation.research.title}</h3>
              <p className="text-gray-400 mb-8 leading-relaxed">
                {innovation.research.description}
              </p>

              <div className="space-y-6">
                {innovation.research.points.map((point, idx) => (
                  <div key={idx} className="flex items-center space-x-4">
                    <div className="w-2 h-2 bg-gradient-to-r from-gray-400 to-yellow-500 rounded-full"></div>
                    <span className="text-gray-300">{point}</span>
                  </div>
                ))}
              </div>
            </div>

            <div className="relative">
              <img
                src={innovation.research.image.src}
                alt={innovation.research.image.alt}
                className="w-full h-96 object-cover rounded-2xl"
                loading="lazy"
              />
              <div className="absolute inset-0 bg-gradient-to-r from-blue-600/20 to-transparent rounded-2xl"></div>
              <div className="absolute top-6 left-6">
                <div className="glass-effect rounded-lg p-4">
                  <div className="text-2xl font-light metallic-gradient mono-font">
                    {innovation.research.badge.value}
                  </div>
                  <div className="text-xs text-gray-400 uppercase tracking-wider">
                    {innovation.research.badge.label}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default InnovationSection;
