import { Globe, Lightbulb, Target, Users } from 'lucide-react';
import React from 'react';
import useContent from '../hooks/useContent';

const AboutPage: React.FC = () => {
  const { sections } = useContent();
  const { about } = sections;
  const iconMap = {
    Target,
    Lightbulb,
    Users,
    Globe,
  } as const;

  return (
    <div className="min-h-screen bg-black text-white">
      <section
        className="relative h-screen flex items-center justify-center overflow-hidden pt-20">
        <div className="absolute inset-0 z-0">
          <img src={about.image} alt="WINASTRA facility" className="w-full h-full object-cover" />
          <div className="absolute inset-0 bg-gradient-to-b from-black/70 via-black/50 to-black/80"></div>
        </div>

        <div className="relative z-10 text-center max-w-4xl mx-auto px-4">
          <h1 className="text-5xl md:text-7xl lg:text-8xl font-thin mb-6 tracking-tight leading-none">
            <span className="metallic-gradient">{about.title}</span>
            <br />
            <span className="text-white font-light">{about.subtitle}</span>
          </h1>
          <p className="text-xl md:text-2xl text-gray-300 mb-8 max-w-2xl mx-auto">
            {about.description}
          </p>
        </div>
      </section>

      <section className="py-20 bg-gradient-to-b from-black to-gray-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-16 items-center">
            <div className="animate-on-scroll">
              <h2 className="text-4xl md:text-5xl font-thin text-white mb-8">Our Story</h2>
              <p className="text-lg text-gray-300 mb-6 leading-relaxed">{about.story.founding}</p>
              <p className="text-lg text-gray-300 mb-6 leading-relaxed">{about.story.growth}</p>
              <p className="text-lg text-gray-300 leading-relaxed">{about.story.future}</p>
            </div>

            <div className="animate-on-scroll">
              <img
                src={about.image}
                alt="WINASTRA manufacturing process"
                className="w-full h-96 object-cover rounded-2xl"
              />
            </div>
          </div>
        </div>
      </section>

      <section className="py-20 bg-gray-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16 animate-on-scroll">
            <h2 className="text-4xl md:text-5xl font-thin text-white mb-6">Our Journey</h2>
            <p className="text-lg text-gray-400 max-w-3xl mx-auto">{about.timelineIntro}</p>
          </div>

          <div className="relative">
            <div className="absolute left-1/2 transform -translate-x-1/2 w-1 h-full bg-gradient-to-b from-gray-400 to-yellow-500"></div>

            {about.milestones.map((milestone, index) => (
              <div
                key={index}
                className={`relative flex items-center mb-12 animate-on-scroll ${index % 2 === 0 ? 'justify-start' : 'justify-end'
                  }`}
                style={{ animationDelay: `${index * 0.2}s` }}>
                <div className={`w-5/12 ${index % 2 === 0 ? 'pr-8 text-right' : 'pl-8 text-left'}`}>
                  <div className="glass-effect rounded-2xl p-6">
                    <div className="text-2xl font-light metallic-gradient mono-font mb-2">
                      {milestone.year}
                    </div>
                    <h3 className="text-xl font-semibold text-white mb-2">{milestone.title}</h3>
                    <p className="text-gray-400">{milestone.description}</p>
                  </div>
                </div>

                <div className="absolute left-1/2 transform -translate-x-1/2 w-4 h-4 bg-gradient-to-r from-gray-400 to-yellow-500 rounded-full border-4 border-gray-900"></div>
              </div>
            ))}
          </div>
        </div>
      </section>

      <section className="py-20 bg-black">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16 animate-on-scroll">
            <h2 className="text-4xl md:text-5xl font-thin text-white mb-6">Our Values</h2>
            <p className="text-lg text-gray-400 max-w-3xl mx-auto">{about.valuesIntro}</p>
          </div>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {about.values.map((value: any, index: number) => {
              const IconComponent = iconMap[value.icon as keyof typeof iconMap] || Target;
              return (
                <div
                  key={index}
                  className="text-center group animate-on-scroll hover-lift"
                  style={{ animationDelay: `${index * 0.2}s` }}>
                  <div className="glass-effect rounded-2xl p-8">
                    <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-gradient-to-r from-gray-400 to-yellow-500 mb-6 group-hover:scale-110 transition-transform duration-300">
                      <IconComponent className="w-8 h-8 text-black" />
                    </div>
                    <h3 className="text-xl font-semibold text-white mb-4">{value.title}</h3>
                    <p className="text-gray-400 leading-relaxed">{value.description}</p>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </section>

      <section className="py-20 bg-gradient-to-b from-black to-gray-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {about.stats.map((stat, index) => (
              <div
                key={index}
                className="text-center animate-on-scroll"
                style={{ animationDelay: `${index * 0.2}s` }}>
                <div className="text-5xl font-light metallic-gradient mono-font mb-2">
                  {stat.number}
                </div>
                <div className="text-gray-400 uppercase tracking-wider text-sm">{stat.label}</div>
              </div>
            ))}
          </div>
        </div>
      </section>

      <section className="py-20 bg-gray-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16 animate-on-scroll">
            <h2 className="text-4xl md:text-5xl font-thin text-white mb-6">Leadership Team</h2>
            <p className="text-lg text-gray-400 max-w-3xl mx-auto">{about.leadership.intro}</p>
          </div>

          <div className="glass-effect rounded-3xl p-8 md:p-12 animate-on-scroll">
            <div className="grid lg:grid-cols-2 gap-12 items-center">
              <div>
                <h3 className="text-3xl font-light text-white mb-6">{about.leadership.title}</h3>
                <p className="text-gray-400 mb-8 leading-relaxed">{about.leadership.description}</p>

                <div className="space-y-6">
                  {about.leadership.highlights.map((highlight: string, idx: number) => (
                    <div key={idx} className="flex items-center space-x-4">
                      <div className="w-2 h-2 bg-gradient-to-r from-gray-400 to-yellow-500 rounded-full"></div>
                      <span className="text-gray-300">{highlight}</span>
                    </div>
                  ))}
                </div>
              </div>

              <div className="relative">
                <img
                  src={about.leadership.image}
                  alt="Leadership team"
                  className="w-full h-96 object-cover rounded-2xl"
                />
                <div className="absolute inset-0 bg-gradient-to-r from-blue-600/20 to-transparent rounded-2xl"></div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default AboutPage;
