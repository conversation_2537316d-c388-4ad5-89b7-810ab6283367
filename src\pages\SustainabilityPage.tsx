import { Droplets, Recycle, Sun, Wind, Zap } from 'lucide-react';
import React from 'react';
import useContent from '../hooks/useContent';

const SustainabilityPage: React.FC = () => {
  const { sections } = useContent();
  const { sustainability } = sections;

  const iconMap = { Droplets, Recycle, Sun, Wind, Zap } as const;

  return (
    <div className="min-h-screen bg-black text-white">
      <section
        id="main-content"
        className="relative h-screen flex items-center justify-center overflow-hidden pt-20">
        <div className="absolute inset-0 z-0">
          <img
            src={sustainability.image}
            alt="Sustainable manufacturing"
            className="w-full h-full object-cover"
          />
          <div className="absolute inset-0 bg-gradient-to-b from-black/70 via-black/50 to-black/80"></div>
        </div>

        <div className="relative z-10 text-center max-w-4xl mx-auto px-4">
          <h1 className="text-5xl md:text-7xl lg:text-8xl font-thin mb-6 tracking-tight leading-none">
            <span className="metallic-gradient">{sustainability.title}</span>
            <br />
            <span className="text-white font-light">{sustainability.subtitle}</span>
          </h1>
          <p className="text-xl md:text-2xl text-gray-300 mb-8 max-w-2xl mx-auto">
            {sustainability.description}
          </p>
        </div>
      </section>

      <section className="py-20 bg-gradient-to-b from-black to-gray-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16 animate-on-scroll">
            <h2 className="text-4xl md:text-5xl font-thin text-white mb-6">Our Initiatives</h2>
            <p className="text-lg text-gray-400 max-w-3xl mx-auto">
              {sustainability.initiativesIntro}
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-8">
            {sustainability.initiatives.map((initiative, index) => {
              const Icon = iconMap[initiative.icon as keyof typeof iconMap];
              return (
                <div
                  key={index}
                  className="group hover-lift animate-on-scroll"
                  style={{ animationDelay: `${index * 0.2}s` }}>
                  <div className="glass-effect rounded-2xl p-8 h-full">
                    <div className="flex items-start space-x-4 mb-6">
                      <div className="w-16 h-16 rounded-full bg-gradient-to-r from-gray-400 to-yellow-500 flex items-center justify-center flex-shrink-0 group-hover:scale-110 transition-transform duration-300">
                        <Icon className="w-8 h-8 text-white" />
                      </div>
                      <div className="flex-1">
                        <h3 className="text-xl font-semibold text-white mb-2">
                          {initiative.title}
                        </h3>
                        <div className="text-2xl font-light metallic-gradient mono-font mb-2">
                          {initiative.metrics}
                        </div>
                      </div>
                    </div>

                    <p className="text-gray-400 mb-4 leading-relaxed">{initiative.description}</p>
                    <p className="text-gray-300 text-sm leading-relaxed">{initiative.details}</p>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </section>

      <section className="py-20 bg-gray-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16 animate-on-scroll">
            <h2 className="text-4xl md:text-5xl font-thin text-white mb-6">2030 Roadmap</h2>
            <p className="text-lg text-gray-400 max-w-3xl mx-auto">{sustainability.goalsIntro}</p>
          </div>

          <div className="space-y-8">
            {sustainability.goals.map((goal, index) => (
              <div
                key={index}
                className="glass-effect rounded-2xl p-6 animate-on-scroll"
                style={{ animationDelay: `${index * 0.2}s` }}>
                <div className="flex items-center justify-between mb-4">
                  <div>
                    <div className="text-2xl font-light metallic-gradient mono-font">
                      {goal.year}
                    </div>
                    <h3 className="text-lg font-semibold text-white">{goal.target}</h3>
                  </div>
                  <div className="text-right">
                    <div className="text-2xl font-light metallic-gradient mono-font">
                      {goal.progress}%
                    </div>
                    <div className="text-sm text-gray-400">Progress</div>
                  </div>
                </div>

                <div className="w-full bg-gray-800 rounded-full h-2">
                  <div
                    className="bg-gradient-to-r from-gray-400 to-yellow-500 h-2 rounded-full transition-all duration-1000"
                    style={{ width: `${goal.progress}%` }}></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      <section className="py-20 bg-black">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-16 items-center">
            <div className="animate-on-scroll">
              <h2 className="text-4xl md:text-5xl font-thin text-white mb-8">
                Environmental Impact
              </h2>
              <p className="text-lg text-gray-300 mb-8 leading-relaxed">
                {sustainability.impact.description}
              </p>

              <div className="grid grid-cols-2 gap-6 mb-8">
                {sustainability.impact.stats.map((stat, index) => (
                  <div key={index} className="text-center">
                    <div className={`text-3xl font-light ${stat.color} mono-font mb-2`}>
                      {stat.value}
                    </div>
                    <div className="text-sm text-gray-400">{stat.label}</div>
                  </div>
                ))}
              </div>

              <div className="space-y-4">
                <h4 className="text-lg font-semibold text-white mb-4">
                  Environmental Certifications
                </h4>
                {sustainability.certifications.map((cert, index) => (
                  <div key={index} className="flex items-center space-x-3">
                    <div className="w-2 h-2 bg-gradient-to-r from-gray-400 to-yellow-500 rounded-full"></div>
                    <span className="text-gray-300">{cert}</span>
                  </div>
                ))}
              </div>
            </div>

            <div className="animate-on-scroll">
              <img
                src={sustainability.impact.image}
                alt="Environmental initiatives"
                className="w-full h-96 object-cover rounded-2xl"
              />
            </div>
          </div>
        </div>
      </section>

      <section className="py-20 bg-gradient-to-b from-black to-gray-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="glass-effect rounded-3xl p-8 md:p-12 animate-on-scroll">
            <div className="text-center mb-12">
              <h3 className="text-3xl font-light text-white mb-6">Green Technology Integration</h3>
              <p className="text-gray-400 max-w-3xl mx-auto leading-relaxed">
                {sustainability.technology.intro}
              </p>
            </div>

            <div className="grid md:grid-cols-3 gap-8">
              {sustainability.technology.items.map((tech, index) => {
                const TechIcon = iconMap[tech.icon as keyof typeof iconMap];
                return (
                  <div key={index} className="text-center">
                    <div className="w-16 h-16 rounded-full bg-gradient-to-r from-gray-400 to-yellow-500 flex items-center justify-center mx-auto mb-4">
                      <TechIcon className="w-8 h-8 text-white" />
                    </div>
                    <h4 className="text-lg font-semibold text-white mb-2">{tech.title}</h4>
                    <p className="text-gray-400 text-sm">{tech.description}</p>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default SustainabilityPage;
