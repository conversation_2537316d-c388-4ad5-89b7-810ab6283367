import { Clock, Mail, MapPin, Phone, Send } from 'lucide-react';
import React, { useState } from 'react';
import { useContent } from '../hooks/useContent';

const ContactSection: React.FC = () => {
  const { sections } = useContent();
  const { contact } = sections;
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    company: '',
    phone: '',
    project: '',
    message: '',
  });

  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle');

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>
  ) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });

    if (errors[name]) {
      setErrors({ ...errors, [name]: '' });
    }
  };

  const validateForm = () => {
    const newErrors: { [key: string]: string } = {};
    if (!formData.name.trim()) {
      newErrors.name = contact.form.fields.name.validation.required;
    }
    if (!formData.email.trim()) {
      newErrors.email = contact.form.fields.email.validation.required;
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = contact.form.fields.email.validation.invalid;
    }
    if (!formData.message.trim()) {
      newErrors.message = contact.form.fields.message.validation.required;
    } else if (formData.message.trim().length < 10) {
      newErrors.message = contact.form.fields.message.validation.minLength;
    }
    return newErrors;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    const formErrors = validateForm();
    if (Object.keys(formErrors).length > 0) {
      setErrors(formErrors);
      return;
    }
    setIsSubmitting(true);
    setErrors({});

    try {
      await new Promise(resolve => setTimeout(resolve, 2000));

      console.log('Form submitted:', formData);
      setSubmitStatus('success');

      setFormData({
        name: '',
        email: '',
        company: '',
        phone: '',
        project: '',
        message: '',
      });

      setTimeout(() => setSubmitStatus('idle'), 5000);
    } catch (error) {
      console.error('Form submission error:', error);
      setSubmitStatus('error');
      setTimeout(() => setSubmitStatus('idle'), 5000);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <section id="contact" className="py-20 bg-gradient-to-b from-black to-gray-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16 animate-on-scroll">
          <h2 className="text-4xl md:text-5xl lg:text-6xl font-thin text-white mb-6">
            {contact.title}
            <span className="block metallic-gradient font-light">{contact.subtitle}</span>
          </h2>
          <p className="text-lg text-gray-400 max-w-3xl mx-auto leading-relaxed">
            {contact.description}
          </p>
        </div>

        <div className="grid lg:grid-cols-2 gap-16">
          {/* Contact Info */}
          <div className="animate-on-scroll">
            <h3 className="text-2xl font-light text-white mb-8">{contact.ui.title}</h3>

            <div className="space-y-6 mb-12">
              {/* Address */}
              <div className="flex items-start space-x-4">
                <div className="w-12 h-12 rounded-full bg-gradient-to-r from-gray-400 to-yellow-500 flex items-center justify-center flex-shrink-0">
                  <MapPin className="w-6 h-6 text-black" />
                </div>
                <div>
                  <h4 className="font-semibold text-white mb-1">{contact.ui.headquarters}</h4>
                  <p className="text-gray-400">
                    {contact.address.street}
                    <br />
                    {contact.address.city}, {contact.address.state} {contact.address.zip}
                  </p>
                </div>
              </div>

              {/* Phone */}
              <div className="flex items-start space-x-4">
                <div className="w-12 h-12 rounded-full bg-gradient-to-r from-gray-400 to-yellow-500 flex items-center justify-center flex-shrink-0">
                  <Phone className="w-6 h-6 text-black" />
                </div>
                <div>
                  <h4 className="font-semibold text-white mb-1">{contact.ui.phone}</h4>
                  <p className="text-gray-400">{contact.phone}</p>
                  {contact.phoneSecondary && (
                    <p className="text-gray-400">{contact.phoneSecondary}</p>
                  )}
                </div>
              </div>

              {/* Email */}
              <div className="flex items-start space-x-4">
                <div className="w-12 h-12 rounded-full bg-gradient-to-r from-gray-400 to-yellow-500 flex items-center justify-center flex-shrink-0">
                  <Mail className="w-6 h-6 text-black" />
                </div>
                <div>
                  <h4 className="font-semibold text-white mb-1">{contact.ui.email}</h4>
                  <p className="text-gray-400">{contact.email}</p>
                  {contact.emailSales && <p className="text-gray-400">{contact.emailSales}</p>}
                </div>
              </div>

              {/* Hours */}
              <div className="flex items-start space-x-4">
                <div className="w-12 h-12 rounded-full bg-gradient-to-r from-gray-400 to-yellow-500 flex items-center justify-center flex-shrink-0">
                  <Clock className="w-6 h-6 text-black" />
                </div>
                <div>
                  <h4 className="font-semibold text-white mb-1">Business Hours</h4>
                  <p className="text-gray-400">{contact.hours.weekdays}</p>
                  <p className="text-gray-400">{contact.hours.weekends}</p>
                </div>
              </div>
            </div>
          </div>

          {/* Contact Form */}
          <div className="animate-on-scroll">
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Name + Email */}
              <div className="grid md:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="name" className="block text-sm font-medium text-gray-300 mb-2">
                    {contact.form.fields.name.label}
                  </label>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    required
                    className={`w-full px-4 py-3 bg-white/5 border rounded-lg focus:outline-none transition-colors duration-200 text-white placeholder-gray-500 ${
                      errors.name
                        ? 'border-red-500 focus:border-red-400'
                        : 'border-gray-700 focus:border-gray-400'
                    }`}
                    placeholder={contact.form.fields.name.placeholder}
                  />
                  {errors.name && (
                    <p className="mt-1 text-sm text-red-400" role="alert">
                      {errors.name}
                    </p>
                  )}
                </div>

                <div>
                  <label htmlFor="email" className="block text-sm font-medium text-gray-300 mb-2">
                    {contact.form.fields.email.label}
                  </label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    required
                    className={`w-full px-4 py-3 bg-white/5 border rounded-lg focus:outline-none transition-colors duration-200 text-white placeholder-gray-500 ${
                      errors.email
                        ? 'border-red-500 focus:border-red-400'
                        : 'border-gray-700 focus:border-gray-400'
                    }`}
                    placeholder={contact.form.fields.email.placeholder}
                  />
                  {errors.email && (
                    <p className="mt-1 text-sm text-red-400" role="alert">
                      {errors.email}
                    </p>
                  )}
                </div>
              </div>

              {/* Company + Phone */}
              <div className="grid md:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="company" className="block text-sm font-medium text-gray-300 mb-2">
                    {contact.form.fields.company.label}
                  </label>
                  <input
                    type="text"
                    id="company"
                    name="company"
                    value={formData.company}
                    onChange={handleInputChange}
                    className="w-full px-4 py-3 bg-white/5 border border-gray-700 rounded-lg focus:border-gray-400 focus:outline-none transition-colors duration-200 text-white placeholder-gray-500"
                    placeholder={contact.form.fields.company.placeholder}
                  />
                </div>

                <div>
                  <label htmlFor="phone" className="block text-sm font-medium text-gray-300 mb-2">
                    {contact.form.fields.phone.label}
                  </label>
                  <input
                    type="tel"
                    id="phone"
                    name="phone"
                    value={formData.phone}
                    onChange={handleInputChange}
                    className="w-full px-4 py-3 bg-white/5 border border-gray-700 rounded-lg focus:border-gray-400 focus:outline-none transition-colors duration-200 text-white placeholder-gray-500"
                    placeholder={contact.form.fields.phone.placeholder}
                  />
                </div>
              </div>

              {/* Project Type */}
              <div>
                <label htmlFor="project" className="block text-sm font-medium text-gray-300 mb-2">
                  {contact.form.fields.project.label}
                </label>
                <select
                  id="project"
                  name="project"
                  value={formData.project}
                  onChange={handleInputChange}
                  className="w-full px-4 py-3 bg-white/5 border border-gray-700 rounded-lg focus:border-gray-400 focus:outline-none transition-colors duration-200 text-white">
                  {contact.form.fields.project.options.map(opt => (
                    <option key={opt.value} value={opt.value}>
                      {opt.label}
                    </option>
                  ))}
                </select>
              </div>

              {/* Message */}
              <div>
                <label htmlFor="message" className="block text-sm font-medium text-gray-300 mb-2">
                  {contact.form.fields.message.label}
                </label>
                <textarea
                  id="message"
                  name="message"
                  value={formData.message}
                  onChange={handleInputChange}
                  rows={5}
                  required
                  className={`w-full px-4 py-3 bg-white/5 border rounded-lg focus:outline-none transition-colors duration-200 text-white placeholder-gray-500 resize-none ${
                    errors.message
                      ? 'border-red-500 focus:border-red-400'
                      : 'border-gray-700 focus:border-gray-400'
                  }`}
                  placeholder={contact.form.fields.message.placeholder}></textarea>
                {errors.message && (
                  <p className="mt-1 text-sm text-red-400" role="alert">
                    {errors.message}
                  </p>
                )}
              </div>

              {/* Submit Button */}
              <button
                type="submit"
                disabled={isSubmitting}
                className={`w-full py-4 px-6 rounded-lg font-medium text-lg tracking-wide flex items-center justify-center space-x-2 transition-all duration-300 ${
                  isSubmitting
                    ? 'bg-gray-600 cursor-not-allowed'
                    : 'metallic-button hover:scale-105'
                }`}>
                {isSubmitting ? (
                  <>
                    <div className="w-5 h-5 border-2 border-black border-t-transparent rounded-full animate-spin"></div>
                    <span>{contact.form.button.submitting}</span>
                  </>
                ) : (
                  <>
                    <Send size={18} />
                    <span>{contact.form.button.idle}</span>
                  </>
                )}
              </button>

              {/* Status Messages */}
              {submitStatus === 'success' && (
                <div
                  className="mt-4 p-4 bg-green-900/50 border border-green-700 rounded-lg text-green-300"
                  role="alert">
                  <p className="font-medium">{contact.form.status.success.title}</p>
                  <p className="text-sm mt-1">{contact.form.status.success.description}</p>
                </div>
              )}

              {submitStatus === 'error' && (
                <div
                  className="mt-4 p-4 bg-red-900/50 border border-red-700 rounded-lg text-red-300"
                  role="alert">
                  <p className="font-medium">{contact.form.status.error.title}</p>
                  <p className="text-sm mt-1">{contact.form.status.error.description}</p>
                </div>
              )}
            </form>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ContactSection;
