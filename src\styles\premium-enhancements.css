:root {
  --color-luxury-platinum: #e5e4e2;
  --color-luxury-champagne: #f7e7ce;
  --color-luxury-rose-gold: #e8b4a0;
  --color-luxury-deep-charcoal: #0d0d0d;
  --color-luxury-midnight: #1a1a1a;
  --color-luxury-accent-gold: #c9a96e;
  --color-luxury-accent-copper: #b87333;
  --font-primary: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
  --font-display: "Inter", serif;
  --font-mono: "JetBrains Mono", monospace;
  --cursor-size: 20px;
  --cursor-trail-size: 40px;
  --cursor-precision-size: 16px;
  --cursor-industrial-size: 24px;
  --space-micro: 0.125rem;
  --space-xs: 0.25rem;
  --space-sm: 0.5rem;
  --space-md: 1rem;
  --space-lg: 1.5rem;
  --space-xl: 2rem;
  --space-2xl: 3rem;
  --space-3xl: 4rem;
  --space-4xl: 6rem;
  --space-5xl: 8rem;
  --shadow-luxury-soft: 0 4px 20px rgba(0, 0, 0, 0.08);
  --shadow-luxury-medium: 0 8px 40px rgba(0, 0, 0, 0.12);
  --shadow-luxury-strong: 0 16px 60px rgba(0, 0, 0, 0.16);
  --shadow-luxury-dramatic: 0 32px 80px rgba(0, 0, 0, 0.24);
  --transition-micro: 150ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-fast: 250ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-medium: 400ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: 600ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-luxury: 800ms cubic-bezier(0.25, 0.46, 0.45, 0.94);
}
.luxury-gradient-copper,
.luxury-gradient-gold,
.luxury-gradient-platinum {
  -webkit-text-fill-color: transparent;
}
.luxury-heading-display {
  font-family: var(--font-display);
  font-weight: 200;
  letter-spacing: -0.02em;
  line-height: 0.9;
  text-rendering: optimizeLegibility;
}
.luxury-heading-primary {
  font-family: var(--font-primary);
  font-weight: 300;
  letter-spacing: -0.01em;
  line-height: 1.1;
}
.luxury-body-large,
.luxury-heading-secondary {
  letter-spacing: 0.01em;
  font-family: var(--font-primary);
}
.luxury-heading-secondary {
  font-weight: 400;
  line-height: 1.2;
}
.luxury-body-large {
  font-weight: 300;
  font-size: 1.125rem;
  line-height: 1.6;
}
.luxury-body-medium {
  font-family: var(--font-primary);
  font-weight: 400;
  font-size: 1rem;
  line-height: 1.5;
  letter-spacing: 0.005em;
}
.luxury-caption {
  font-family: var(--font-mono);
  font-weight: 400;
  font-size: 0.75rem;
  line-height: 1.4;
  letter-spacing: 0.1em;
  text-transform: uppercase;
}
.luxury-button-primary,
.luxury-button-secondary {
  padding: var(--space-md) var(--space-xl);
  font-size: 0.95rem;
  letter-spacing: 0.02em;
  position: relative;
  transition: all var(--transition-medium);
  overflow: hidden;
}
.luxury-gradient-platinum {
  background: linear-gradient(
    135deg,
    #f8f8ff 0,
    #e5e4e2 25%,
    #d3d3d3 50%,
    silver 75%,
    #a8a8a8 100%
  );
  -webkit-background-clip: text;
  background-clip: text;
}
.luxury-gradient-gold {
  background: linear-gradient(135deg, gold 0, #ffed4e 25%, #d4af37 50%, #b8860b 75%, #8b7355 100%);
  -webkit-background-clip: text;
  background-clip: text;
}
.luxury-gradient-copper {
  background: linear-gradient(
    135deg,
    #e87722 0,
    #d2691e 25%,
    #b87333 50%,
    sienna 75%,
    #8b4513 100%
  );
  -webkit-background-clip: text;
  background-clip: text;
}
.luxury-glass-primary {
  background: rgba(255, 255, 255, 0.03);
  backdrop-filter: blur(20px) saturate(180%);
  border: 1px solid rgba(255, 255, 255, 0.08);
  box-shadow: var(--shadow-luxury-soft);
}
.luxury-glass-secondary {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(16px) saturate(160%);
  border: 1px solid rgba(255, 255, 255, 0.12);
  box-shadow: var(--shadow-luxury-medium);
}
.luxury-glass-accent {
  background: rgba(212, 175, 55, 0.08);
  backdrop-filter: blur(24px) saturate(200%);
  border: 1px solid rgba(212, 175, 55, 0.16);
  box-shadow: var(--shadow-luxury-strong);
}
.luxury-button-primary {
  background: linear-gradient(
    135deg,
    var(--color-luxury-platinum) 0,
    #f0f0f0 50%,
    var(--color-luxury-platinum) 100%
  );
  color: var(--color-luxury-deep-charcoal);
  border: none;
  border-radius: 0.75rem;
  font-weight: 500;
  box-shadow: var(--shadow-luxury-soft);
}
.luxury-button-primary::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left var(--transition-slow);
}
.luxury-button-primary:hover::before,
.luxury-glow-trail:hover::before {
  left: 100%;
}
.luxury-button-primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-luxury-medium);
  background: linear-gradient(
    135deg,
    var(--color-luxury-accent-gold) 0,
    #f4d03f 50%,
    var(--color-luxury-accent-gold) 100%
  );
}
.luxury-button-secondary {
  background: 0 0;
  color: var(--color-luxury-platinum);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 0.75rem;
  font-weight: 400;
}
.luxury-button-secondary::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 0;
  height: 100%;
  background: rgba(255, 255, 255, 0.05);
  transition: width var(--transition-medium);
  z-index: -1;
}
.luxury-button-secondary:hover::before {
  width: 100%;
}
.luxury-button-secondary:hover {
  border-color: rgba(255, 255, 255, 0.4);
  transform: translateY(-1px);
  box-shadow: var(--shadow-luxury-soft);
}
@keyframes luxuryFadeInUp {
  0% {
    opacity: 0;
    transform: translateY(40px) scale(0.98);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}
@keyframes luxurySlideInLeft {
  0% {
    opacity: 0;
    transform: translateX(-60px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}
@keyframes luxurySlideInRight {
  0% {
    opacity: 0;
    transform: translateX(60px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}
@keyframes luxuryScaleIn {
  0% {
    opacity: 0;
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}
@keyframes luxuryFloat {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-8px);
  }
}
@keyframes luxuryShimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}
.luxury-animate-fade-up {
  animation: luxuryFadeInUp var(--transition-luxury) ease-out forwards;
}
.luxury-animate-slide-left {
  animation: luxurySlideInLeft var(--transition-medium) ease-out forwards;
}
.luxury-animate-slide-right {
  animation: luxurySlideInRight var(--transition-medium) ease-out forwards;
}
.luxury-animate-scale {
  animation: luxuryScaleIn var(--transition-medium) ease-out forwards;
}
.luxury-animate-float {
  animation: 4s ease-in-out infinite luxuryFloat;
}
.luxury-shimmer {
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  background-size: 200% 100%;
  animation: 2s infinite luxuryShimmer;
}
.luxury-hover-lift {
  transition: all var(--transition-medium);
}
.luxury-hover-lift:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: var(--shadow-luxury-dramatic);
}
.luxury-hover-glow {
  transition: all var(--transition-medium);
  position: relative;
}
.luxury-hover-glow::before {
  content: "";
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(
    45deg,
    var(--color-luxury-accent-gold),
    var(--color-luxury-accent-copper),
    var(--color-luxury-accent-gold)
  );
  border-radius: inherit;
  opacity: 0;
  transition: opacity var(--transition-medium);
  z-index: -1;
  filter: blur(8px);
}
.luxury-hover-glow:hover::before {
  opacity: 0.3;
}
.luxury-grid-masonry {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  grid-gap: var(--space-xl);
  grid-auto-rows: masonry;
}
.luxury-grid-featured {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr;
  grid-template-rows: 1fr 1fr;
  grid-gap: var(--space-lg);
  height: 600px;
}
.luxury-grid-featured .featured-main {
  grid-row: 1/3;
}
@media (max-width: 1200px) {
  .luxury-grid-featured {
    grid-template-columns: 1fr 1fr;
    grid-template-rows: 2fr 1fr 1fr;
  }
  .luxury-grid-featured .featured-main {
    grid-column: 1/3;
    grid-row: 1;
  }
}
@media (max-width: 768px) {
  .luxury-grid-featured {
    grid-template-columns: 1fr;
    grid-template-rows: auto;
    height: auto;
  }
  .luxury-grid-featured .featured-main {
    grid-column: 1;
    grid-row: auto;
  }
  .luxury-heading-display {
    font-size: clamp(2.5rem, 8vw, 4rem);
  }
  .luxury-body-large {
    font-size: 1rem;
  }
}
.luxury-skeleton {
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0.05) 25%,
    rgba(255, 255, 255, 0.1) 50%,
    rgba(255, 255, 255, 0.05) 75%
  );
  background-size: 200% 100%;
  animation: 1.5s infinite luxuryShimmer;
  border-radius: 0.5rem;
}
.luxury-scroll-indicator {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background: linear-gradient(
    90deg,
    var(--color-luxury-accent-gold),
    var(--color-luxury-accent-copper)
  );
  transform-origin: left;
  z-index: 1000;
}
.luxury-focus:focus-visible {
  outline: 2px solid var(--color-luxury-accent-gold);
  outline-offset: 2px;
  box-shadow: 0 0 0 4px rgba(212, 175, 55, 0.2);
}
::selection {
  background: var(--color-luxury-accent-gold);
  color: var(--color-luxury-deep-charcoal);
}
::-moz-selection {
  background: var(--color-luxury-accent-gold);
  color: var(--color-luxury-deep-charcoal);
}
::-webkit-scrollbar {
  width: 8px;
}
::-webkit-scrollbar-track {
  background: var(--color-luxury-midnight);
}
::-webkit-scrollbar-thumb {
  background: linear-gradient(
    180deg,
    var(--color-luxury-accent-gold),
    var(--color-luxury-accent-copper)
  );
  border-radius: 4px;
}
::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(
    180deg,
    var(--color-luxury-accent-copper),
    var(--color-luxury-accent-gold)
  );
}
.luxury-button-micro {
  position: relative;
  overflow: hidden;
  transition: all var(--transition-medium);
}
.luxury-button-micro::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0, transparent 70%);
  transition: all var(--transition-fast);
  transform: translate(-50%, -50%);
  border-radius: 50%;
}
.luxury-glow-trail::before,
.luxury-text-reveal::after {
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  position: absolute;
  content: "";
}
.luxury-button-micro:hover::before,
.luxury-ripple:active::after {
  width: 300px;
  height: 300px;
}
.luxury-button-micro:active {
  transform: scale(0.98);
}
.luxury-card-hover {
  transition: all var(--transition-medium);
  transform-style: preserve-3d;
}
.luxury-card-hover:hover {
  transform: translateY(-8px) rotateX(5deg);
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.3),
    0 0 20px rgba(212, 175, 55, 0.2);
}
.luxury-text-reveal {
  overflow: hidden;
  position: relative;
}
.luxury-text-reveal::after {
  background: linear-gradient(90deg, transparent, rgba(212, 175, 55, 0.4), transparent);
  animation: 2s ease-in-out textReveal;
}
@keyframes textReveal {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}
.luxury-pulse {
  animation: 2s cubic-bezier(0.4, 0, 0.6, 1) infinite luxuryPulse;
}
@keyframes luxuryPulse {
  0%,
  100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.05);
  }
}
.luxury-breathing {
  animation: 4s ease-in-out infinite luxuryBreathing;
}
@keyframes luxuryBreathing {
  0%,
  100% {
    transform: scale(1);
    box-shadow: 0 0 20px rgba(212, 175, 55, 0.3);
  }
  50% {
    transform: scale(1.02);
    box-shadow: 0 0 40px rgba(212, 175, 55, 0.5);
  }
}
.luxury-magnetic {
  transition: transform var(--transition-fast);
  cursor: pointer;
}
.luxury-magnetic:hover {
  transform: translate(var(--mouse-x, 0), var(--mouse-y, 0));
}
.luxury-parallax {
  transform: translateZ(0);
  will-change: transform;
}
.custom-cursor,
.custom-cursor-trail {
  will-change: transform, opacity;
  backface-visibility: hidden;
}
.luxury-stagger-children > * {
  opacity: 0;
  transform: translateY(30px);
  animation: luxuryStaggerIn var(--transition-luxury) ease-out forwards;
}
.luxury-stagger-children > :first-child {
  animation-delay: 0.1s;
}
.luxury-stagger-children > :nth-child(2) {
  animation-delay: 0.2s;
}
.luxury-stagger-children > :nth-child(3) {
  animation-delay: 0.3s;
}
.luxury-stagger-children > :nth-child(4) {
  animation-delay: 0.4s;
}
.luxury-stagger-children > :nth-child(5) {
  animation-delay: 0.5s;
}
.luxury-stagger-children > :nth-child(6) {
  animation-delay: 0.6s;
}
@keyframes luxuryStaggerIn {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
.luxury-morph {
  transition: all var(--transition-slow);
  border-radius: 20px;
}
.luxury-morph:hover {
  border-radius: 50px;
  transform: rotate(5deg);
}
.luxury-glow-trail,
.luxury-ripple {
  position: relative;
  overflow: hidden;
}
.luxury-glow-trail::before {
  background: linear-gradient(90deg, transparent, rgba(212, 175, 55, 0.6), transparent);
  transition: left var(--transition-slow);
}
.luxury-elastic {
  transition: transform var(--transition-medium) var(--ease-elastic);
}
.luxury-elastic:hover {
  transform: scale(1.1);
}
.luxury-tilt {
  transition: transform var(--transition-medium);
  transform-style: preserve-3d;
}
.luxury-tilt:hover {
  transform: perspective(1000px) rotateX(10deg) rotateY(10deg);
}
.luxury-ripple::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transform: translate(-50%, -50%);
  transition:
    width var(--transition-fast),
    height var(--transition-fast);
}
.custom-cursor {
  width: var(--cursor-size);
  height: var(--cursor-size);
  border-radius: 50%;
  background: radial-gradient(circle, var(--color-luxury-accent-gold) 0, transparent 70%);
  border: 2px solid var(--color-luxury-accent-gold);
  transition: all var(--transition-fast);
  transform-origin: center;
}
.custom-cursor.hovering {
  transform: scale(1.5);
  background: radial-gradient(circle, var(--color-luxury-platinum) 0, transparent 70%);
  border-color: var(--color-luxury-platinum);
  box-shadow: 0 0 20px rgba(255, 255, 255, 0.3);
}
.custom-cursor.clicking {
  transform: scale(0.8);
  background: radial-gradient(circle, var(--color-luxury-accent-copper) 0, transparent 70%);
  border-color: var(--color-luxury-accent-copper);
  box-shadow: 0 0 15px rgba(184, 115, 51, 0.5);
}
.custom-cursor.cursor-precision {
  width: var(--cursor-precision-size);
  height: var(--cursor-precision-size);
  background: conic-gradient(
    from 0deg,
    var(--color-luxury-accent-gold),
    var(--color-luxury-platinum),
    var(--color-luxury-accent-gold)
  );
  border: 1px solid var(--color-luxury-accent-gold);
}
.custom-cursor.cursor-industrial {
  width: var(--cursor-industrial-size);
  height: var(--cursor-industrial-size);
  background: linear-gradient(
    45deg,
    var(--color-luxury-accent-copper),
    var(--color-luxury-accent-gold)
  );
  border: 2px solid var(--color-luxury-deep-charcoal);
  border-radius: 4px;
}
.custom-cursor.cursor-text {
  width: 2px;
  height: 20px;
  background: var(--color-luxury-accent-gold);
  border-radius: 1px;
  border: none;
}
.custom-cursor-trail {
  transition:
    transform 0.15s ease-out,
    opacity 0.2s ease-out;
}
body * {
  cursor: none !important;
}
.hover-lift {
  transition:
    transform 0.3s,
    box-shadow 0.3s;
  will-change: transform;
}
.hover-lift:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}
@keyframes ripple-animation {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 0;
  }
}
.scroll-animation {
  opacity: 0;
  transition: all var(--transition-luxury);
}
.scroll-animation.visible {
  opacity: 1;
}
.scroll-animation.fadeUp {
  transform: translateY(60px);
}
.scroll-animation.fadeUp.visible {
  transform: translateY(0);
}
.scroll-animation.slideLeft {
  transform: translateX(-60px);
}
.scroll-animation.slideLeft.visible,
.scroll-animation.slideRight.visible {
  transform: translateX(0);
}
.scroll-animation.slideRight {
  transform: translateX(60px);
}
.scroll-animation.scale {
  transform: scale(0.8);
}
.scroll-animation.scale.visible {
  transform: scale(1);
}
.luxury-loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-lg);
}
.luxury-loading-animation {
  position: relative;
}
.luxury-industrial,
.luxury-metallic {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}
.luxury-metallic-gear {
  width: 100%;
  height: 100%;
  border: 3px solid var(--color-luxury-accent-gold);
  border-radius: 50%;
  position: relative;
  animation: 2s linear infinite luxury-rotate;
}
.luxury-metallic-gear-inner {
  width: 60%;
  height: 60%;
  border: 2px solid var(--color-luxury-platinum);
  border-radius: 50%;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  animation: 1.5s linear infinite luxury-rotate-reverse;
}
.luxury-metallic-particles {
  position: absolute;
  width: 100%;
  height: 100%;
}
.luxury-metallic-particle {
  position: absolute;
  width: 4px;
  height: 4px;
  background: var(--color-luxury-accent-gold);
  border-radius: 50%;
  top: 50%;
  left: 50%;
  transform-origin: 0 0;
  animation: 1s ease-in-out infinite luxury-particle-pulse;
}
.luxury-industrial-frame {
  width: 100%;
  height: 100%;
  border: 3px solid var(--color-luxury-accent-copper);
  border-radius: 8px;
  position: relative;
  background: linear-gradient(
    45deg,
    var(--color-luxury-deep-charcoal),
    var(--color-luxury-midnight)
  );
}
.luxury-industrial-bolt,
.luxury-loading-dot {
  background: var(--color-luxury-accent-gold);
  border-radius: 50%;
}
.luxury-industrial-bolt {
  position: absolute;
  width: 6px;
  height: 6px;
  animation: 1.5s ease-in-out infinite luxury-bolt-glow;
}
.luxury-industrial-bolt-1 {
  top: 4px;
  left: 4px;
  animation-delay: 0s;
}
.luxury-industrial-bolt-2 {
  top: 4px;
  right: 4px;
  animation-delay: 375ms;
}
.luxury-industrial-bolt-3 {
  bottom: 4px;
  right: 4px;
  animation-delay: 0.75s;
}
.luxury-industrial-bolt-4 {
  bottom: 4px;
  left: 4px;
  animation-delay: 1.125s;
}
.luxury-industrial-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 60%;
  height: 60%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.luxury-industrial-logo {
  font-family: var(--font-mono);
  font-weight: 700;
  color: var(--color-luxury-accent-gold);
  font-size: 1.2em;
  animation: 2s ease-in-out infinite luxury-logo-pulse;
}
.luxury-loading-text {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  font-family: var(--font-primary);
  color: var(--color-luxury-platinum);
  font-weight: 300;
  letter-spacing: 0.05em;
}
.luxury-loading-dots,
.luxury-progress-container {
  gap: var(--space-xs);
  display: flex;
}
.luxury-loading-dot {
  width: 4px;
  height: 4px;
  opacity: 0.3;
  transition: opacity var(--transition-fast);
}
.luxury-loading-dot.active {
  opacity: 1;
  animation: 0.5s ease-in-out luxury-dot-pulse;
}
.luxury-progress-container {
  width: 200px;
  flex-direction: column;
}
.luxury-progress-bar {
  width: 100%;
  height: 4px;
  background: var(--color-luxury-midnight);
  border-radius: 2px;
  overflow: hidden;
}
.luxury-progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--color-luxury-accent-gold), var(--color-luxury-platinum));
  border-radius: 2px;
  transition: width var(--transition-medium);
  position: relative;
}
.luxury-progress-fill::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: 1.5s ease-in-out infinite luxury-progress-shimmer;
}
.luxury-progress-text {
  text-align: center;
  font-family: var(--font-mono);
  font-size: 0.875rem;
  color: var(--color-luxury-accent-gold);
}
@keyframes luxury-rotate {
  0% {
    transform: rotate(0);
  }
  100% {
    transform: rotate(360deg);
  }
}
@keyframes luxury-rotate-reverse {
  0% {
    transform: translate(-50%, -50%) rotate(0);
  }
  100% {
    transform: translate(-50%, -50%) rotate(-360deg);
  }
}
@keyframes luxury-particle-pulse {
  0%,
  100% {
    opacity: 0.3;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.2);
  }
}
@keyframes luxury-bolt-glow {
  0%,
  100% {
    box-shadow: 0 0 5px var(--color-luxury-accent-gold);
    opacity: 0.7;
  }
  50% {
    box-shadow: 0 0 15px var(--color-luxury-accent-gold);
    opacity: 1;
  }
}
@keyframes luxury-logo-pulse {
  0%,
  100% {
    transform: scale(1);
    opacity: 0.8;
  }
  50% {
    transform: scale(1.1);
    opacity: 1;
  }
}
@keyframes luxury-dot-pulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.3);
  }
}
@keyframes luxury-progress-shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}
.luxury-skeleton-container {
  display: flex;
  flex-direction: column;
  gap: var(--space-sm);
}
.luxury-skeleton-line {
  background: linear-gradient(
    90deg,
    var(--color-luxury-midnight) 25%,
    var(--color-luxury-graphite) 50%,
    var(--color-luxury-midnight) 75%
  );
  background-size: 200% 100%;
  border-radius: 4px;
  animation: 1.5s ease-in-out infinite luxury-skeleton-shimmer;
}
@keyframes luxury-skeleton-shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}
@media (prefers-reduced-motion: reduce) {
  .luxury-animate-fade-up,
  .luxury-animate-float,
  .luxury-animate-scale,
  .luxury-animate-slide-left,
  .luxury-animate-slide-right,
  .luxury-breathing,
  .luxury-pulse,
  .luxury-shimmer,
  .luxury-stagger-children > *,
  .luxury-text-reveal::after {
    animation: none !important;
    transition: none !important;
  }
  .luxury-card-hover:hover,
  .luxury-elastic:hover,
  .luxury-tilt:hover {
    transform: none !important;
  }
  .custom-cursor,
  .custom-cursor-trail {
    display: none !important;
  }
  body * {
    cursor: auto !important;
  }
}
