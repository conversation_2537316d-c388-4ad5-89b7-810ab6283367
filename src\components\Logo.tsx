import React, { useState } from 'react';
import { useContent } from '../hooks/useContent';

interface LogoProps {
  variant?: 'primary' | 'secondary' | 'icon' | 'text';
  size?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
  showText?: boolean;
  onClick?: () => void;
}

const Logo: React.FC<LogoProps> = ({
  variant = 'primary',
  size = 'md',
  className = '',
  showText = false,
  onClick,
}) => {
  const { company, ui } = useContent();
  const [imageError, setImageError] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  const sizeClasses = {
    sm: 'w-8 h-8',
    md: 'w-12 h-12',
    lg: 'w-16 h-16',
    xl: 'w-24 h-24',
  };

  const textSizeClasses = {
    sm: 'text-sm',
    md: 'text-lg',
    lg: 'text-xl',
    xl: 'text-3xl',
  };

  // Logo image paths - using the actual logo files from assets
  const logoImages = {
    primary: new URL('../assets/IMG-20250901-WA0003.jpg', import.meta.url).href, // Main logo
    secondary: new URL('../assets/IMG-20250901-WA0004.jpg', import.meta.url).href, // Alternative version
    icon: new URL('../assets/IMG-20250901-WA0005.jpg', import.meta.url).href, // Icon only
    text: new URL('../assets/IMG-20250901-WA0006.jpg', import.meta.url).href, // Text only
  };

  const renderLogo = () => {
    if (variant === 'text' || imageError) {
      return (
        <span
          className={`font-bold text-gradient-brand tracking-wider ${textSizeClasses[size]} transition-all duration-300`}>
          {company.name}
        </span>
      );
    }

    return (
      <div className="flex items-center space-x-3">
        <div className="relative">
          {isLoading && (
            <div
              className={`${sizeClasses[size]} bg-gray-200 dark:bg-gray-700 animate-pulse rounded-lg flex items-center justify-center`}>
              <div className="w-4 h-4 bg-gray-300 dark:bg-gray-600 rounded animate-pulse"></div>
            </div>
          )}
          <img
            src={logoImages[variant]}
            alt={ui.accessibility.logoAlt}
            className={`${sizeClasses[size]} object-contain transition-all duration-300 hover:scale-105 ${isLoading ? 'opacity-0 absolute inset-0' : 'opacity-100'}`}
            onLoad={() => setIsLoading(false)}
            onError={() => {
              setImageError(true);
              setIsLoading(false);
            }}
            loading="eager"
          />
        </div>
        {showText && (
          <span
            className={`font-bold text-gradient-brand tracking-wider ${textSizeClasses[size]} transition-all duration-300`}>
            {company.name}
          </span>
        )}
      </div>
    );
  };

  const handleClick = () => {
    if (onClick) {
      onClick();
    }
  };

  if (onClick) {
    return (
      <button
        onClick={handleClick}
        className={`flex items-center transition-all duration-200 hover:scale-105 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 ${className}`}
        aria-label={`${company.name} logo - Go to homepage`}>
        {renderLogo()}
      </button>
    );
  }

  return <div className={`flex items-center ${className}`}>{renderLogo()}</div>;
};

export default Logo;
