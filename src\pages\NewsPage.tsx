import { ArrowRight, Calendar, Tag, User } from 'lucide-react';
import React, { useState } from 'react';
import useContent from '../hooks/useContent';

const NewsPage: React.FC = () => {
  const { sections } = useContent();
  const { news } = sections;

  const [activeCategory, setActiveCategory] = useState('All');
  const categories = news.categories;
  const articles = news.articles;
  const filteredArticles =
    activeCategory === 'All'
      ? articles
      : articles.filter(article => article.category === activeCategory);
  const featuredArticles = articles.filter(article => article.featured);
  const regularArticles = filteredArticles.filter(article => !article.featured);

  return (
    <div className="min-h-screen bg-black text-white">
      <section
        id="main-content"
        className="relative h-screen flex items-center justify-center overflow-hidden pt-20">
        <div className="absolute inset-0 z-0">
          <img src={news.image} alt="News and insights" className="w-full h-full object-cover" />
          <div className="absolute inset-0 bg-gradient-to-b from-black/70 via-black/50 to-black/80"></div>
        </div>

        <div className="relative z-10 text-center max-w-4xl mx-auto px-4">
          <h1 className="text-5xl md:text-7xl lg:text-8xl font-thin mb-6 tracking-tight leading-none">
            <span className="metallic-gradient">{news.title}</span>
            <br />
            <span className="text-white font-light">{news.subtitle}</span>
          </h1>
          <p className="text-xl md:text-2xl text-gray-300 mb-8 max-w-2xl mx-auto">
            {news.description}
          </p>
        </div>
      </section>

      <section className="py-12 bg-gradient-to-b from-black to-gray-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-wrap justify-center gap-4 animate-on-scroll">
            {categories.map(category => (
              <button
                key={category}
                onClick={() => setActiveCategory(category)}
                className={`px-6 py-3 rounded-full text-sm font-medium transition-all duration-300 ${activeCategory === category
                    ? 'metallic-button'
                    : 'glass-effect text-gray-300 hover:text-white'
                  }`}>
                {category}
              </button>
            ))}
          </div>
        </div>
      </section>

      {activeCategory === 'All' && (
        <section className="py-20 bg-gray-900">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16 animate-on-scroll">
              <h2 className="text-4xl md:text-5xl font-thin text-white mb-6">
                {news.featuredTitle}
              </h2>
            </div>

            <div className="grid lg:grid-cols-2 gap-8">
              {featuredArticles.map((article, index) => (
                <div
                  key={article.id}
                  className="group hover-lift animate-on-scroll"
                  style={{ animationDelay: `${index * 0.2}s` }}>
                  <div className="glass-effect rounded-2xl overflow-hidden h-full">
                    <div className="relative overflow-hidden">
                      <img
                        src={article.image}
                        alt={article.title}
                        className="w-full h-64 object-cover group-hover:scale-110 transition-transform duration-500"
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-transparent to-transparent"></div>
                      <div className="absolute top-4 left-4">
                        <span className="px-3 py-1 bg-gradient-to-r from-gray-400 to-yellow-500 text-black text-xs rounded-full font-medium">
                          Featured
                        </span>
                      </div>
                    </div>

                    <div className="p-6">
                      <div className="flex items-center space-x-4 mb-4 text-sm text-gray-400">
                        <span className="flex items-center space-x-1">
                          <Tag size={14} />
                          <span>{article.category}</span>
                        </span>
                        <span className="flex items-center space-x-1">
                          <Calendar size={14} />
                          <span>{new Date(article.date).toLocaleDateString()}</span>
                        </span>
                        <span>{article.readTime}</span>
                      </div>

                      <h3 className="text-xl font-semibold text-white mb-3 group-hover:text-gray-300 transition-colors duration-300">
                        {article.title}
                      </h3>

                      <p className="text-gray-400 mb-4 leading-relaxed">{article.excerpt}</p>

                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2 text-sm text-gray-400">
                          <User size={14} />
                          <span>{article.author}</span>
                        </div>

                        <button className="flex items-center space-x-2 text-gray-400 hover:text-white transition-colors duration-300 group">
                          <span className="text-sm">Read More</span>
                          <ArrowRight
                            size={16}
                            className="group-hover:translate-x-1 transition-transform duration-300"
                          />
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>
      )}

      <section className="py-20 bg-black">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {regularArticles.map((article, index) => (
              <div
                key={article.id}
                className="group hover-lift animate-on-scroll"
                style={{ animationDelay: `${index * 0.1}s` }}>
                <div className="glass-effect rounded-2xl overflow-hidden h-full">
                  <div className="relative overflow-hidden">
                    <img
                      src={article.image}
                      alt={article.title}
                      className="w-full h-48 object-cover group-hover:scale-110 transition-transform duration-500"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent"></div>
                    <div className="absolute top-4 left-4">
                      <span className="px-3 py-1 bg-white/10 backdrop-blur-md text-white text-xs rounded-full">
                        {article.category}
                      </span>
                    </div>
                  </div>

                  <div className="p-6">
                    <div className="flex items-center space-x-4 mb-3 text-xs text-gray-400">
                      <span className="flex items-center space-x-1">
                        <Calendar size={12} />
                        <span>{new Date(article.date).toLocaleDateString()}</span>
                      </span>
                      <span>{article.readTime}</span>
                    </div>

                    <h3 className="text-lg font-semibold text-white mb-3 group-hover:text-gray-300 transition-colors duration-300">
                      {article.title}
                    </h3>

                    <p className="text-gray-400 mb-4 leading-relaxed text-sm">{article.excerpt}</p>

                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2 text-xs text-gray-400">
                        <User size={12} />
                        <span>{article.author}</span>
                      </div>

                      <button className="flex items-center space-x-2 text-gray-400 hover:text-white transition-colors duration-300 group">
                        <span className="text-xs">Read</span>
                        <ArrowRight
                          size={14}
                          className="group-hover:translate-x-1 transition-transform duration-300"
                        />
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      <section className="py-20 bg-gradient-to-b from-black to-gray-900">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="glass-effect rounded-3xl p-8 md:p-12 animate-on-scroll">
            <h3 className="text-3xl font-light text-white mb-6">{news.newsletter.title}</h3>
            <p className="text-gray-400 mb-8 leading-relaxed">{news.newsletter.description}</p>

            <div className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
              <input
                type="email"
                placeholder={news.newsletter.placeholder}
                className="flex-1 px-4 py-3 bg-white/5 border border-gray-700 rounded-lg focus:border-gray-400 focus:outline-none text-white placeholder-gray-500"
              />
              <button className="metallic-button px-6 py-3 rounded-lg font-medium">
                {news.newsletter.button}
              </button>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default NewsPage;
