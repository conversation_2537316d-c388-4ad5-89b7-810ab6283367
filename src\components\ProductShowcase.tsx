import { <PERSON><PERSON><PERSON>, <PERSON>, Maximize2, <PERSON> } from 'lucide-react';
import React, { useState } from 'react';
import { PRODUCT_SHOWCASE } from '../content/index';

const ProductShowcase: React.FC = () => {
  const content = PRODUCT_SHOWCASE;
  const [activeCategory, setActiveCategory] = useState('Featured');
  const [hoveredProduct, setHoveredProduct] = useState<number | null>(null);

  const filteredProducts =
    activeCategory === 'Featured'
      ? content.products.filter(p => p.featured)
      : content.products.filter(p => p.category === activeCategory);

  const renderStars = (rating: number) =>
    Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        size={14}
        className={i < rating ? 'text-yellow-400 fill-current' : 'text-gray-600'}
      />
    ));

  return (
    <section id="products" className="py-16 bg-gradient-to-b from-black via-gray-900 to-black">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-10 luxury-animate-fade-up">
          <div className="flex items-center justify-center mb-6">
            <div className="w-12 h-0.5 bg-gradient-to-r from-transparent to-yellow-400"></div>
            <span className="luxury-caption text-gray-400 mx-6">{content.section.caption}</span>
            <div className="w-12 h-0.5 bg-gradient-to-l from-transparent to-yellow-400"></div>
          </div>

          <h2 className="luxury-heading-display text-5xl md:text-7xl mb-8">
            <span className="luxury-gradient-platinum">{content.section.heading.primary}</span>
            <span className="luxury-gradient-gold block font-light">
              {content.section.heading.secondary}
            </span>
          </h2>

          <p className="luxury-body-large text-gray-300 max-w-4xl mx-auto leading-relaxed">
            {content.section.description}
          </p>
        </div>

        <div className="flex flex-wrap justify-center gap-4 mb-16 luxury-animate-fade-up">
          {content.categories.map(category => (
            <button
              key={category}
              onClick={() => setActiveCategory(category)}
              className={`px-8 py-4 rounded-full text-sm font-medium transition-all duration-500 relative overflow-hidden group ${activeCategory === category
                ? 'luxury-button-primary'
                : 'luxury-glass-primary text-gray-300 hover:text-white luxury-hover-glow'
                }`}
              aria-pressed={activeCategory === category}>
              <span className="relative z-10">{category}</span>
              {activeCategory !== category && (
                <div className="absolute inset-0 bg-gradient-to-r from-yellow-400/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              )}
            </button>
          ))}
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {filteredProducts.map((product, index) => (
            <div
              key={product.id}
              className="group luxury-hover-lift luxury-animate-fade-up"
              style={{ animationDelay: `${index * 0.1}s` }}
              onMouseEnter={() => setHoveredProduct(product.id)}
              onMouseLeave={() => setHoveredProduct(null)}>
              <div className="luxury-glass-secondary rounded-3xl overflow-hidden h-full relative">
                {product.featured && (
                  <div className="absolute top-4 left-4 z-20">
                    <span className="luxury-glass-accent px-3 py-1 rounded-full text-xs font-medium text-yellow-400 luxury-shimmer">
                      Featured
                    </span>
                  </div>
                )}

                <div className="relative overflow-hidden h-64">
                  <img
                    src={product.image}
                    alt={product.title}
                    className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"
                  />

                  <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                    <div className="absolute bottom-4 right-4 flex space-x-2">
                      <button className="w-10 h-10 rounded-full luxury-glass-accent flex items-center justify-center luxury-hover-glow">
                        <Eye size={16} className="text-yellow-400" />
                      </button>
                      <button className="w-10 h-10 rounded-full luxury-glass-accent flex items-center justify-center luxury-hover-glow">
                        <Maximize2 size={16} className="text-yellow-400" />
                      </button>
                    </div>
                  </div>

                  { }
                  <div className="absolute top-4 right-4">
                    <span className="luxury-glass-primary px-3 py-1 rounded-full text-xs font-medium text-gray-300">
                      {product.price}
                    </span>
                  </div>
                </div>

                <div className="p-8">
                  <div className="flex items-center space-x-1 mb-3">
                    {renderStars(product.rating)}
                    <span className="text-xs text-gray-500 ml-2">({product.rating}.0)</span>
                  </div>

                  <div className="mb-4">
                    <span className="luxury-caption text-yellow-400 mb-2 block">
                      {product.category}
                    </span>
                    <h3 className="luxury-heading-secondary text-xl text-white mb-3 group-hover:luxury-gradient-gold transition-all duration-300">
                      {product.title}
                    </h3>
                  </div>

                  <p className="luxury-body-medium text-gray-400 mb-6 leading-relaxed">
                    {product.description}
                  </p>

                  <div className="mb-6">
                    <h4 className="text-sm font-semibold text-white mb-3">Specifications</h4>
                    <div className="flex flex-wrap gap-2">
                      {product.specs.map((spec, idx) => (
                        <span
                          key={idx}
                          className="px-3 py-1 luxury-glass-primary rounded-lg text-xs text-gray-300">
                          {spec}
                        </span>
                      ))}
                    </div>
                  </div>

                  <div className="mb-6">
                    <h4 className="text-sm font-semibold text-white mb-3">Applications</h4>
                    <div className="space-y-1">
                      {product.applications.slice(0, 2).map((app, idx) => (
                        <div key={idx} className="flex items-center text-xs text-gray-400">
                          <div className="w-1 h-1 bg-yellow-400 rounded-full mr-2"></div>
                          {app}
                        </div>
                      ))}
                      {product.applications.length > 2 && (
                        <div className="text-xs text-gray-500">
                          +{product.applications.length - 2} more
                        </div>
                      )}
                    </div>
                  </div>

                  <div className="flex items-center justify-between">
                    <button className="luxury-button-secondary text-sm px-6 py-2 group">
                      <span className="flex items-center space-x-2">
                        <span>{content.cta.card.learnMore}</span>
                        <ArrowRight
                          size={14}
                          className="group-hover:translate-x-1 transition-transform duration-300"
                        />
                      </span>
                    </button>

                    <button className="text-yellow-400 hover:text-yellow-300 transition-colors duration-300">
                      <span className="text-xs">{content.cta.card.requestQuote}</span>
                    </button>
                  </div>
                </div>

                <div
                  className={`absolute inset-0 rounded-3xl transition-opacity duration-500 pointer-events-none ${hoveredProduct === product.id ? 'opacity-100' : 'opacity-0'
                    }`}>
                  <div className="absolute inset-0 rounded-3xl bg-gradient-to-r from-yellow-400/5 to-transparent"></div>
                </div>
              </div>
            </div>
          ))}
        </div>

        <div className="text-center mt-16 luxury-animate-fade-up">
          <div className="luxury-glass-accent rounded-3xl p-12 max-w-4xl mx-auto">
            <h3 className="luxury-heading-secondary text-3xl text-white mb-6">
              {content.cta.unique.title}
            </h3>
            <p className="luxury-body-large text-gray-300 mb-8 max-w-2xl mx-auto">
              {content.cta.unique.description}
            </p>
            <button className="luxury-button-primary">{content.cta.unique.button}</button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ProductShowcase;