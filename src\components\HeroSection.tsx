import { Sparkles } from 'lucide-react';
import React, { useEffect, useState } from 'react';
import useContent from '../hooks/useContent';
import { useScrollY } from '../hooks/useScroll';
import ResponsiveImage from './ResponsiveImage';

const HeroSection: React.FC = () => {
  const { hero } = useContent();
  const homeHero = hero.home;
  const scrollY = useScrollY();
  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    setIsLoaded(true);
  }, []);

  return (
    <section
      id="home"
      className="relative h-screen flex items-center justify-center overflow-hidden pt-20">
      <div className="absolute inset-0 z-0">
        <div className="relative w-full h-full">
          <div
            style={{
              transform: `translateY(${scrollY * 0.3}px) scale(${1 + scrollY * 0.0002})`,
              filter: 'brightness(0.7) contrast(1.1)',
            }}>
            <ResponsiveImage
              src={homeHero.image}
              alt={homeHero.image}
              className="w-full h-full"
              loading="eager"
              priority={true}
              width={1920}
              height={1080}
              context="hero"
              description={homeHero.image}
            />
          </div>

          {/* Gradient overlays */}
          <div className="absolute inset-0 bg-gradient-to-b from-black/80 via-black/40 to-black/90"></div>
          <div className="absolute inset-0 bg-gradient-to-r from-black/60 via-transparent to-black/60"></div>

          {/* Premium Pattern Overlay */}
          <div
            className="absolute inset-0 opacity-5"
            style={{
              backgroundImage: `linear-gradient(45deg, rgba(212, 175, 55, 0.1) 25%, transparent 25%),
                               linear-gradient(-45deg, rgba(192, 192, 192, 0.1) 25%, transparent 25%)`,
              backgroundSize: '60px 60px',
            }}></div>
        </div>
      </div>

      <div className="relative z-10 text-center max-w-7xl mx-auto px-4 pt-20 pb-12 flex flex-col justify-center">
        <div
          className={`transition-all duration-1200 ${isLoaded ? 'luxury-animate-fade-up' : 'opacity-0'
            }`}>
          {/* Brand */}
          <div className="flex items-center justify-center mb-8">
            <Sparkles className="w-6 h-6 text-yellow-400 mr-3 luxury-animate-float" />
            <span className="text-sm font-semibold tracking-wider uppercase luxury-caption text-gray-300">
              {homeHero.brandMark}
            </span>
            <Sparkles
              className="w-6 h-6 text-yellow-400 ml-3 luxury-animate-float"
              style={{ animationDelay: '1s' }}
            />
          </div>

          {/* Heading */}
          <h1 className="text-4xl md:text-6xl lg:text-7xl font-thin mb-6 tracking-tight leading-tight luxury-heading-display">
            <span className="block drop-shadow-xl luxury-gradient-platinum">
              {homeHero.title.primary}
            </span>
            <span className="block font-light mt-1 drop-shadow-lg luxury-gradient-gold">
              {homeHero.title.secondary}
            </span>
          </h1>

          {/* Subtitle */}
          <div className="mb-8" style={{ animationDelay: '0.9s' }}>
            <p className="text-xl md:text-2xl mb-4 max-w-4xl mx-auto font-medium drop-shadow-lg luxury-body-large text-gray-200">
              {homeHero.subtitle}
            </p>
            <div className="w-32 h-0.5 bg-gradient-to-r from-transparent via-yellow-400 to-transparent mx-auto"></div>
          </div>

          {/* Description */}
          <p
            className="text-lg mb-20 max-w-3xl mx-auto leading-relaxed drop-shadow-lg luxury-body text-gray-300"
            style={{ animationDelay: '1.2s' }}>
            {homeHero.description}
          </p>
        </div>
      </div>

      <div className="absolute bottom-6 left-1/2 transform -translate-x-1/2">
        <div className="flex flex-col items-center space-y-3 luxury-animate-float">
          <div className="w-6 h-10 border-2 border-gray-300 rounded-full flex justify-center">
            <div className="w-1 h-3 bg-gray-300 rounded-full mt-2 animate-bounce"></div>
          </div>
        </div>
      </div>

      <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-yellow-400/10 rounded-full blur-3xl luxury-animate-float"></div>
      <div
        className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-blue-400/10 rounded-full blur-3xl luxury-animate-float"
        style={{ animationDelay: '2s' }}></div>
    </section>
  );
};

export default HeroSection;
