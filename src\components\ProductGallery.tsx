import { ArrowRight, Download, Eye, Maximize2, Shopping<PERSON><PERSON>, Star } from 'lucide-react';
import React, { useState } from 'react';
import { Product, PRODUCT_GALLERY } from '../content';

const ProductGallery: React.FC = () => {
  const content = PRODUCT_GALLERY;
  const [activeCategory, setActiveCategory] = useState('All');
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);

  const filteredProducts =
    activeCategory === 'All'
      ? content.product
      : content.product.filter(product => product.category === activeCategory);

  const handleCategoryChange = (category: string) => {
    if (category === activeCategory) return;

    setIsTransitioning(true);
    setTimeout(() => {
      setActiveCategory(category);
      setIsTransitioning(false);
    }, 150);
  };

  const openProductModal = (product: Product) => {
    setSelectedProduct(product);
    document.body.style.overflow = 'hidden';
  };

  const closeProductModal = () => {
    setSelectedProduct(null);
    document.body.style.overflow = 'unset';
  };

  return (
    <section id="products" className="py-20 bg-black">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16 animate-on-scroll">
          <h2 className="text-4xl md:text-5xl lg:text-6xl font-thin text-white mb-6">
            {content.section.title}
            <span className="block metallic-gradient font-light">{content.section.highlight}</span>
          </h2>
          <p className="text-lg text-gray-400 max-w-3xl mx-auto leading-relaxed">
            {content.section.description}
          </p>
        </div>

        <div className="flex flex-wrap justify-center gap-4 mb-12 animate-on-scroll">
          {content.categories.map((category: string) => (
            <button
              key={category}
              onClick={() => handleCategoryChange(category)}
              className={`px-8 py-4 rounded-full text-sm font-medium transition-all duration-300 ${activeCategory === category
                ? 'metallic-button shadow-lg transform scale-105'
                : 'glass-effect text-gray-300 hover:text-white hover:scale-105'
                }`}
              aria-pressed={activeCategory === category}>
              {category}
              <span className="ml-2 text-xs opacity-75">
                (
                {category === 'All'
                  ? content.product.length
                  : content.product.filter((p: Product) => p.category === category).length}
                )
              </span>
            </button>
          ))}
        </div>

        <div
          className={`grid md:grid-cols-2 lg:grid-cols-2 gap-8 transition-opacity duration-300 ${isTransitioning ? 'opacity-50' : 'opacity-100'
            }`}>
          {filteredProducts.map((product: Product, index: number) => (
            <div
              key={product.id}
              className="group hover-lift animate-on-scroll"
              style={{ animationDelay: `${index * 0.1}s` }}>
              <div className="glass-effect rounded-2xl overflow-hidden h-full relative">
                { }
                {product.featured && (
                  <div className="absolute top-4 left-4 z-10">
                    <span className="px-3 py-1 bg-gradient-to-r from-primary-orange-400 to-primary-orange-600 text-white text-xs font-bold rounded-full">
                      {content.ui.featuredBadge}
                    </span>
                  </div>
                )}

                { }
                <div className="relative overflow-hidden">
                  <img
                    src={product.image}
                    alt={`${product.title} - High-quality aluminum ${product.category.toLowerCase()} for industrial applications`}
                    className="w-full h-64 object-cover group-hover:scale-110 transition-transform duration-500"
                    loading="lazy"
                  />
                  { }
                  <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <div className="absolute bottom-4 right-4 flex space-x-2">
                      <button
                        onClick={() => openProductModal(product)}
                        className="w-10 h-10 rounded-full bg-white/20 backdrop-blur-md flex items-center justify-center hover:bg-white/30 transition-colors duration-200"
                        aria-label="View product details">
                        <Eye className="w-5 h-5 text-white" />
                      </button>
                      <button className="w-10 h-10 rounded-full bg-white/20 backdrop-blur-md flex items-center justify-center hover:bg-white/30 transition-colors duration-200">
                        <Maximize2 className="w-5 h-5 text-white" />
                      </button>
                    </div>
                  </div>
                  { }
                  <div className="absolute top-4 right-4">
                    <div className="flex items-center space-x-1 bg-black/50 backdrop-blur-sm rounded-full px-2 py-1">
                      {[...Array(product.rating)].map((_, i) => (
                        <Star key={i} className="w-3 h-3 text-yellow-400 fill-current" />
                      ))}
                      <span className="text-white text-xs ml-1">{product.rating}.0</span>
                    </div>
                  </div>
                </div>

                { }
                <div className="p-6">
                  <div className="flex items-center justify-between mb-2">
                    <span className="px-2 py-1 bg-gray-800 rounded text-xs text-gray-300 font-medium">
                      {product.category}
                    </span>
                    <span
                      className={`text-xs font-medium ${product.availability === content.ui.availability.inStock
                        ? 'text-green-400'
                        : 'text-yellow-400'
                        }`}>
                      {product.availability}
                    </span>
                  </div>

                  <h3 className="text-xl font-semibold text-white mb-3 line-clamp-2">
                    {product.title}
                  </h3>
                  <p className="text-gray-400 mb-4 leading-relaxed text-sm line-clamp-3">
                    {product.description}
                  </p>

                  { }
                  <div className="mb-4">
                    <h4 className="text-sm font-semibold text-white mb-2">
                      {content.ui.specs.heading}
                    </h4>
                    <div className="grid grid-cols-2 gap-2 text-xs">
                      <div className="flex justify-between">
                        <span className="text-gray-400">{content.ui.specs.alloy}:</span>
                        <span className="text-gray-300 font-medium">{product.specs.alloy}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">{content.ui.specs.tolerance}:</span>
                        <span className="text-gray-300 font-medium">{product.specs.tolerance}</span>
                      </div>
                    </div>
                  </div>

                  { }
                  <div className="mb-4 p-3 bg-gray-800/50 rounded-lg">
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-sm text-gray-400">{content.ui.modal.priceRange}:</span>
                      <span className="text-sm font-semibold text-white">{product.price}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-400">{content.ui.modal.leadTime}:</span>
                      <span className="text-sm text-gray-300">{product.leadTime}</span>
                    </div>
                  </div>

                  <div className="flex items-center justify-between">
                    <button
                      onClick={() => openProductModal(product)}
                      className="flex items-center space-x-2 text-gray-400 hover:text-white transition-colors duration-300 group">
                      <span className="text-sm font-medium">{content.ui.viewDetails}</span>
                      <ArrowRight
                        size={16}
                        className="group-hover:translate-x-1 transition-transform duration-300"
                      />
                    </button>

                    <button className="px-4 py-2 bg-gradient-to-r from-gray-400 to-yellow-500 text-black text-sm font-semibold rounded-lg hover:scale-105 transition-transform duration-200 flex items-center space-x-2">
                      <ShoppingCart size={14} />
                      <span>{content.ui.quoteButton}</span>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {selectedProduct && (
          <div className="fixed inset-0 bg-black/90 backdrop-blur-sm z-50 flex items-center justify-center p-4">
            <div className="bg-gray-900 rounded-2xl max-w-6xl w-full max-h-[90vh] overflow-y-auto">
              <div className="p-8">
                <div className="flex justify-between items-start mb-6">
                  <div>
                    <h3 className="text-3xl font-semibold text-white mb-2">
                      {selectedProduct.title}
                    </h3>
                    <div className="flex items-center space-x-4">
                      <span className="px-3 py-1 bg-gray-800 rounded-full text-sm text-gray-300">
                        {selectedProduct.category}
                      </span>
                      <div className="flex items-center space-x-1">
                        {[...Array(selectedProduct.rating)].map((_, i) => (
                          <Star key={i} className="w-4 h-4 text-yellow-400 fill-current" />
                        ))}
                        <span className="text-gray-400 text-sm ml-1">
                          ({selectedProduct.rating}.0)
                        </span>
                      </div>
                    </div>
                  </div>
                  <button
                    onClick={closeProductModal}
                    className="text-gray-400 hover:text-white transition-colors duration-200 text-2xl">
                    {content.ui.modal.close}
                  </button>
                </div>

                <div className="grid lg:grid-cols-2 gap-8">
                  <div>
                    <img
                      src={selectedProduct.image}
                      alt={selectedProduct.title}
                      className="w-full h-80 object-cover rounded-lg mb-6"
                    />
                    <div className="mb-6">
                      <h4 className="text-lg font-semibold text-white mb-3">
                        {content.ui.modal.description}
                      </h4>
                      <p className="text-gray-300 leading-relaxed">{selectedProduct.description}</p>
                    </div>

                    <div className="mb-6">
                      <h4 className="text-lg font-semibold text-white mb-3">
                        {content.ui.modal.applications}
                      </h4>
                      <div className="grid grid-cols-1 gap-2">
                        {selectedProduct.specs.applications.map((app: string, index: number) => (
                          <div key={index} className="text-gray-300 text-sm flex items-center">
                            <div className="w-1.5 h-1.5 bg-primary-orange-400 rounded-full mr-3"></div>
                            {app}
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>

                  <div>
                    <div className="mb-6">
                      <h4 className="text-lg font-semibold text-white mb-4">
                        {content.ui.modal.technicalSpecs}
                      </h4>
                      <div className="space-y-3">
                        {Object.entries(selectedProduct.technicalData).map(([key, value]) => (
                          <div
                            key={key}
                            className="flex justify-between py-2 border-b border-gray-700">
                            <span className="text-gray-400 capitalize">
                              {key.replace(/([A-Z])/g, ' $1')}:
                            </span>
                            <span className="text-gray-300 font-medium">{value}</span>
                          </div>
                        ))}
                      </div>
                    </div>

                    <div className="mb-6">
                      <h4 className="text-lg font-semibold text-white mb-4">
                        {content.ui.modal.certifications}
                      </h4>
                      <div className="flex flex-wrap gap-2">
                        {selectedProduct.certifications.map((cert: string, index: number) => (
                          <span
                            key={index}
                            className="px-3 py-1 bg-green-900/30 border border-green-700 rounded-full text-green-300 text-sm">
                            {cert}
                          </span>
                        ))}
                      </div>
                    </div>

                    <div className="mb-6 p-4 bg-gray-800 rounded-lg">
                      <h4 className="text-lg font-semibold text-white mb-3">
                        {content.ui.modal.pricing}
                      </h4>
                      <div className="space-y-2">
                        <div className="flex justify-between">
                          <span className="text-gray-400">{content.ui.modal.priceRange}:</span>
                          <span className="text-white font-semibold">{selectedProduct.price}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-400">{content.ui.modal.availability}:</span>
                          <span
                            className={`font-medium ${selectedProduct.availability === content.ui.availability.inStock
                              ? 'text-green-400'
                              : 'text-yellow-400'
                              }`}>
                            {selectedProduct.availability}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-400">{content.ui.modal.leadTime}:</span>
                          <span className="text-gray-300">{selectedProduct.leadTime}</span>
                        </div>
                      </div>
                    </div>

                    <div className="flex space-x-4">
                      <button className="flex-1 bg-gradient-to-r from-gray-400 to-yellow-500 text-black py-4 rounded-lg font-semibold hover:scale-105 transition-transform duration-200 flex items-center justify-center space-x-2">
                        <ShoppingCart size={18} />
                        <span>{content.ui.modal.requestQuote}</span>
                      </button>
                      <button className="flex items-center space-x-2 px-6 py-4 border border-gray-600 rounded-lg text-gray-300 hover:text-white transition-colors duration-200">
                        <Download size={16} />
                        <span>{content.ui.modal.datasheet}</span>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </section>
  );
};

export default ProductGallery;