import React, { useEffect, useState } from 'react';
import { useContent } from '../hooks/useContent';

const LoadingScreen: React.FC<{ onComplete: () => void }> = ({ onComplete }) => {
  const [progress, setProgress] = useState(0);
  const { company, ui } = useContent();

  useEffect(() => {
    const timer = setInterval(() => {
      setProgress(prev => {
        if (prev >= 100) {
          clearInterval(timer);
          setTimeout(onComplete, 500);
          return 100;
        }
        return prev + 2;
      });
    }, 25);

    return () => clearInterval(timer);
  }, [onComplete]);

  return (
    <div className="fixed inset-0 bg-black z-50 flex items-center justify-center">
      <div className="text-center">
        <div className="loading-spinner mx-auto mb-8"></div>
        <h2 className="text-2xl font-light metallic-gradient mb-4">{company.name}</h2>
        <div className="w-64 h-1 bg-gray-800 rounded-full overflow-hidden">
          <div
            className="h-full bg-gradient-to-r from-primary-purple-400 to-primary-orange-500 transition-all duration-300 ease-out"
            style={{ width: `${progress}%` }}></div>
        </div>
        <p className="text-gray-400 text-sm mt-4 mono-font">
          {progress}
          {ui.loading.progress}
        </p>
      </div>
    </div>
  );
};

export default LoadingScreen;
