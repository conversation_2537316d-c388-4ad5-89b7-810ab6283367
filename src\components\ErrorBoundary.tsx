import { Alert<PERSON><PERSON>gle, Home, Mail, RefreshCw } from 'lucide-react';
import { Component, ErrorInfo, ReactNode } from 'react';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
  level?: 'page' | 'section' | 'component';
  showDetails?: boolean;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  errorId: string;
  retryCount: number;
}

class ErrorBoundary extends Component<Props, State> {
  private maxRetries = 3;

  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: '',
      retryCount: 0,
    };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    const errorId = `error_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    return {
      hasError: true,
      error,
      errorId,
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('Error caught by boundary:', error, errorInfo);

    this.setState({
      errorInfo,
    });

    this.props.onError?.(error, errorInfo);

    if (import.meta.env.PROD) {
      this.reportError(error, errorInfo);
    }
  }

  private reportError = (error: Error, errorInfo: ErrorInfo) => {
    const errorReport = {
      message: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      errorId: this.state.errorId,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href,
      level: this.props.level || 'component',
    };

    if (import.meta.env.DEV) {
      console.group('🚨 Error Report');
      console.error('Error ID:', errorReport.errorId);
      console.error('Message:', errorReport.message);
      console.error('Level:', errorReport.level);
      console.groupEnd();
    }
  };

  private handleRetry = () => {
    if (this.state.retryCount < this.maxRetries) {
      this.setState(prevState => ({
        hasError: false,
        error: null,
        errorInfo: null,
        errorId: '',
        retryCount: prevState.retryCount + 1,
      }));
    }
  };

  private handleReload = () => {
    window.location.reload();
  };

  private handleGoHome = () => {
    window.location.href = '/';
  };

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback;
      }

      const { level = 'page' } = this.props;
      const { error, errorId, retryCount } = this.state;

      if (level === 'page') {
        return (
          <div className="min-h-screen bg-primary flex items-center justify-center px-4">
            <div className="max-w-md w-full text-center">
              <div className="mb-8">
                <AlertTriangle className="w-16 h-16 text-error mx-auto mb-4" />
                <h1 className="text-3xl font-bold text-primary mb-2">Something went wrong</h1>
                <p className="text-secondary">
                  We're sorry, but something unexpected happened. Our team has been notified.
                </p>
              </div>

              <div className="bg-surface-secondary/50 rounded-lg p-4 mb-6 text-left">
                <p className="text-sm text-muted mb-2">Error ID: {errorId}</p>
                <p className="text-sm text-error font-mono">
                  {error?.message || 'Unknown error occurred'}
                </p>
              </div>

              <div className="space-y-3">
                <button
                  onClick={this.handleRetry}
                  disabled={retryCount >= this.maxRetries}
                  className="w-full flex items-center justify-center space-x-2 btn btn-primary disabled:opacity-50 disabled:cursor-not-allowed">
                  <RefreshCw className="w-4 h-4" />
                  <span>Try Again ({this.maxRetries - retryCount} attempts left)</span>
                </button>

                <button
                  onClick={this.handleGoHome}
                  className="w-full flex items-center justify-center space-x-2 btn btn-secondary">
                  <Home className="w-4 h-4" />
                  <span>Go to Homepage</span>
                </button>

                <button
                  onClick={this.handleReload}
                  className="w-full flex items-center justify-center space-x-2 btn btn-ghost">
                  <RefreshCw className="w-4 h-4" />
                  <span>Reload Page</span>
                </button>
              </div>

              <div className="mt-8 pt-6 border-t border-primary">
                <p className="text-sm text-muted mb-2">Need help?</p>
                <a
                  href="mailto:<EMAIL>"
                  className="inline-flex items-center space-x-2 text-primary hover:text-secondary transition-colors">
                  <Mail className="w-4 h-4" />
                  <span>Contact Support</span>
                </a>
              </div>

              {import.meta.env.DEV && error && this.props.showDetails && (
                <details className="mt-8 text-left">
                  <summary className="cursor-pointer text-error mb-2">
                    Error Details (Development Only)
                  </summary>
                  <pre className="bg-surface-tertiary p-4 rounded text-xs overflow-auto">
                    {error.toString()}
                    {this.state.errorInfo?.componentStack}
                  </pre>
                </details>
              )}
            </div>
          </div>
        );
      }
      return (
        <div className="bg-surface-secondary border border-primary rounded p-4 my-2">
          <div className="flex items-center space-x-2">
            <AlertTriangle className="w-5 h-5 text-error" />
            <span className="text-sm text-primary">Component failed to load</span>
            <button
              onClick={this.handleRetry}
              disabled={retryCount >= this.maxRetries}
              className="ml-auto text-xs btn btn-primary disabled:opacity-50 px-2 py-1">
              Retry
            </button>
          </div>
        </div>
      );
    }
    return this.props.children;
  }
}

export default ErrorBoundary;
