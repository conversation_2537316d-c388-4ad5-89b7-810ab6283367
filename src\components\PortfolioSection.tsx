import { Calendar, ExternalLink, MapPin } from 'lucide-react';
import React, { useState } from 'react';
import { PORTFOLIO } from '../content';

const PortfolioSection: React.FC = () => {
  const portfolio = PORTFOLIO;
  const [activeProject, setActiveProject] = useState(0);

  return (
    <section id="portfolio" className="py-20 bg-gradient-to-b from-gray-900 to-black">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16 animate-on-scroll">
          <h2 className="text-4xl md:text-5xl lg:text-6xl font-thin text-white mb-6">
            {portfolio.section.title}
            <span className="block metallic-gradient font-light">
              {portfolio.section.highlight}
            </span>
          </h2>
          <p className="text-lg text-gray-400 max-w-3xl mx-auto leading-relaxed">
            {portfolio.section.description}
          </p>
        </div>

        <div className="grid lg:grid-cols-2 gap-12 items-center">
          <div className="space-y-6 animate-on-scroll">
            {portfolio.projects.map((project, index) => (
              <div
                key={index}
                className={`cursor-pointer transition-all duration-300 ${activeProject === index
                  ? 'glass-effect rounded-2xl p-6'
                  : 'p-6 hover:bg-white/5 rounded-2xl'
                  }`}
                onClick={() => setActiveProject(index)}>
                <div className="flex items-start space-x-4">
                  <div className="flex-shrink-0">
                    <div
                      className={`w-12 h-12 rounded-full flex items-center justify-center ${activeProject === index
                        ? 'bg-gradient-to-r from-gray-400 to-yellow-500'
                        : 'bg-gray-800'
                        }`}>
                      <span className="text-white font-semibold">{index + 1}</span>
                    </div>
                  </div>
                  <div className="flex-1">
                    <h3
                      className={`text-xl font-semibold mb-2 ${activeProject === index ? 'text-white' : 'text-gray-400'
                        }`}>
                      {project.title}
                    </h3>
                    <div className="flex items-center space-x-4 text-sm text-gray-500 mb-2">
                      <span className="flex items-center space-x-1">
                        <MapPin size={14} />
                        <span>{project.location}</span>
                      </span>
                      <span className="flex items-center space-x-1">
                        <Calendar size={14} />
                        <span>{project.year}</span>
                      </span>
                    </div>
                    <p
                      className={`text-sm leading-relaxed ${activeProject === index ? 'text-gray-300' : 'text-gray-500'
                        }`}>
                      {project.description}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>

          <div className="animate-on-scroll">
            <div className="relative overflow-hidden rounded-3xl">
              <img
                src={portfolio.projects[activeProject].image}
                alt={`${portfolio.projects[activeProject].title} - Aluminum manufacturing project`}
                className="w-full h-96 object-cover"
                loading="lazy"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-transparent to-transparent"></div>
              <div className="absolute bottom-6 left-6 right-6">
                <span className="inline-block px-3 py-1 bg-white/10 backdrop-blur-md rounded-full text-xs text-white mb-3">
                  {portfolio.projects[activeProject].category}
                </span>
                <h3 className="text-2xl font-semibold text-white mb-2">
                  {portfolio.projects[activeProject].title}
                </h3>
                <p className="text-gray-300 text-sm leading-relaxed">
                  {portfolio.projects[activeProject].details}
                </p>
              </div>
            </div>

            <div className="mt-6 glass-effect rounded-2xl p-6">
              <h4 className="text-lg font-semibold text-white mb-4">
                {portfolio.ui.technicalSpecs}
              </h4>
              <div className="flex flex-wrap gap-2 mb-6">
                {portfolio.projects[activeProject].specs.map((spec, index) => (
                  <span
                    key={index}
                    className="px-3 py-2 bg-gray-800 rounded-lg text-sm text-gray-300">
                    {spec}
                  </span>
                ))}
              </div>
              <button className="flex items-center space-x-2 text-gray-400 hover:text-white transition-colors duration-300 group">
                <span className="text-sm">{portfolio.ui.caseStudy}</span>
                <ExternalLink
                  size={16}
                  className="group-hover:translate-x-1 transition-transform duration-300"
                />
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default PortfolioSection;