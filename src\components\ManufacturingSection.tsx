import { Award, CheckCircle, Clock, Cog, Shield, Target, Users, Zap } from 'lucide-react';
import React from 'react';
import { MANUFACTURING } from '../content';

const ManufacturingSection: React.FC = () => {
  const manufacturing = MANUFACTURING;
  const iconMap: Record<string, React.ElementType> = {
    Cog,
    Zap,
    Shield,
    Award,
    Target,
    CheckCircle,
    Clock,
    Users,
  };

  return (
    <section id="manufacturing" className="py-20 bg-gradient-to-b from-black to-gray-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16 animate-on-scroll">
          <h2 className="text-4xl md:text-5xl lg:text-6xl font-thin text-white mb-6">
            {manufacturing.section.title}
            <span className="block metallic-gradient font-light">
              {manufacturing.section.highlight}
            </span>
          </h2>
          <p className="text-lg text-gray-400 max-w-3xl mx-auto leading-relaxed">
            {manufacturing.section.description}
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-20">
          {manufacturing.processes.map((process, index) => {
            const Icon = iconMap[process.icon];
            return (
              <div
                key={index}
                className="group hover-lift animate-on-scroll"
                style={{ animationDelay: `${index * 0.2}s` }}>
                <div className="glass-effect rounded-2xl p-6 h-full">
                  <div className="relative overflow-hidden rounded-lg mb-6">
                    <img
                      src={process.image}
                      alt={`${process.title} - Advanced aluminum manufacturing process`}
                      className="w-full h-48 object-cover group-hover:scale-110 transition-transform duration-500"
                      loading="lazy"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"></div>
                    <div className="absolute top-4 left-4">
                      <div className="w-12 h-12 rounded-full bg-white/10 backdrop-blur-md flex items-center justify-center">
                        <Icon className="w-6 h-6 text-white" />
                      </div>
                    </div>
                  </div>

                  <h3 className="text-xl font-semibold text-white mb-3">{process.title}</h3>
                  <p className="text-gray-400 leading-relaxed mb-4 text-sm">
                    {process.description}
                  </p>

                  {/* Capabilities */}
                  <div className="mb-4">
                    <h4 className="text-sm font-semibold text-white mb-2">Capabilities</h4>
                    <ul className="space-y-1">
                      {process.capabilities.slice(0, 3).map((capability, capIndex) => (
                        <li key={capIndex} className="text-xs text-gray-300 flex items-center">
                          <div className="w-1 h-1 bg-yellow-400 rounded-full mr-2"></div>
                          {capability}
                        </li>
                      ))}
                    </ul>
                  </div>

                  {/* Specs */}
                  <div className="border-t border-gray-700 pt-3">
                    <h4 className="text-sm font-semibold text-white mb-2">Key Specs</h4>
                    <div className="space-y-1">
                      {Object.entries(process.specifications)
                        .slice(0, 2)
                        .map(([key, value]) => (
                          <div key={key} className="flex justify-between text-xs">
                            <span className="text-gray-400 capitalize">{key}:</span>
                            <span className="text-gray-300">{value}</span>
                          </div>
                        ))}
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-20">
          {manufacturing.capabilities.map((capability, index) => {
            const Icon = iconMap[capability.icon];
            return (
              <div
                key={index}
                className="text-center animate-on-scroll"
                style={{ animationDelay: `${index * 0.2}s` }}>
                <div className="glass-effect rounded-2xl p-8 hover-lift">
                  <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-gradient-to-r from-gray-400 to-yellow-500 mb-4">
                    <Icon className="w-8 h-8 text-black" />
                  </div>
                  <div className="text-4xl font-light metallic-gradient mono-font mb-2">
                    {capability.value}
                  </div>
                  <div className="text-lg font-semibold text-white mb-2">{capability.title}</div>
                  <div className="text-sm text-gray-400">{capability.description}</div>
                </div>
              </div>
            );
          })}
        </div>

        <div className="glass-effect rounded-3xl p-8 md:p-12 animate-on-scroll mb-20">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div>
              <h3 className="text-3xl font-light text-white mb-6">
                {manufacturing.facility.heading}
              </h3>
              <p className="text-gray-400 mb-8 leading-relaxed">
                {manufacturing.facility.description}
              </p>

              <div className="grid grid-cols-2 gap-6 mb-8">
                {manufacturing.facility.stats.map((stat, index) => (
                  <div key={index} className="text-center">
                    <div className="text-3xl font-light metallic-gradient mono-font mb-2">
                      {stat.value}
                    </div>
                    <div className="text-sm text-gray-500 uppercase tracking-wider">
                      {stat.label}
                    </div>
                  </div>
                ))}
              </div>

              <div className="space-y-4">
                <h4 className="text-lg font-semibold text-white">Key Equipment</h4>
                <div className="grid grid-cols-1 gap-2">
                  {manufacturing.facility.equipmentList.map((equipment, index) => (
                    <div key={index} className="flex items-center space-x-3">
                      <div className="w-2 h-2 bg-gradient-to-r from-gray-400 to-yellow-500 rounded-full"></div>
                      <span className="text-gray-300 text-sm">{equipment}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            <div className="relative">
              <img
                src={manufacturing.facility.image}
                alt="Advanced aluminum manufacturing facility overview"
                className="w-full h-96 object-cover rounded-2xl"
                loading="lazy"
              />
              <div className="absolute inset-0 bg-gradient-to-r from-blue-600/20 to-transparent rounded-2xl"></div>
              <div className="absolute top-6 left-6">
                <div className="glass-effect rounded-lg p-4">
                  <div className="text-2xl font-light metallic-gradient mono-font">
                    {manufacturing.facility.qualityRate}
                  </div>
                  <div className="text-xs text-gray-400 uppercase tracking-wider">Quality Rate</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="text-center mb-12 animate-on-scroll">
          <h3 className="text-3xl font-light text-white mb-8">
            Quality Certifications & Standards
          </h3>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4 max-w-4xl mx-auto">
            {manufacturing.certifications.map((cert, index) => (
              <div key={index} className="glass-effect rounded-lg p-4 hover-lift">
                <div className="flex items-center space-x-3">
                  <CheckCircle className="w-5 h-5 text-green-400 flex-shrink-0" />
                  <span className="text-gray-300 text-sm font-medium">{cert}</span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default ManufacturingSection;
