import { ArrowRight, Award, Briefcase, Clock, MapPin, Users } from 'lucide-react';
import React from 'react';
import useContent from '../hooks/useContent';

const CareersPage: React.FC = () => {
  const { sections } = useContent();
  const { careers } = sections;
  const iconMap = {
    Award,
    Users,
    Clock,
    Briefcase,
  } as const;

  return (
    <div className="min-h-screen bg-black text-white">
      <section
        className="relative h-screen flex items-center justify-center overflow-hidden pt-20">
        <div className="absolute inset-0 z-0">
          <img
            src={careers.image}
            alt="Careers at WINASTRA"
            className="w-full h-full object-cover"
          />
          <div className="absolute inset-0 bg-gradient-to-b from-black/70 via-black/50 to-black/80"></div>
        </div>

        <div className="relative z-10 text-center max-w-4xl mx-auto px-4">
          <h1 className="text-5xl md:text-7xl lg:text-8xl font-thin mb-6 tracking-tight leading-none">
            <span className="metallic-gradient">{careers.title}</span>
            <br />
            <span className="text-white font-light">{careers.subtitle}</span>
          </h1>
          <p className="text-xl md:text-2xl text-gray-300 mb-8 max-w-2xl mx-auto">
            {careers.description}
          </p>
        </div>
      </section>

      <section className="py-20 bg-gradient-to-b from-black to-gray-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16 animate-on-scroll">
            <h2 className="text-4xl md:text-5xl font-thin text-white mb-6">{careers.why.title}</h2>
            <p className="text-lg text-gray-400 max-w-3xl mx-auto">{careers.why.description}</p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {careers.why.benefits.map((benefit, index) => {
              const IconComponent = iconMap[benefit.icon as keyof typeof iconMap] || Award;
              return (
                <div
                  key={index}
                  className="text-center group animate-on-scroll hover-lift"
                  style={{ animationDelay: `${index * 0.2}s` }}>
                  <div className="glass-effect rounded-2xl p-8">
                    <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-gradient-to-r from-gray-400 to-yellow-500 mb-6 group-hover:scale-110 transition-transform duration-300">
                      <IconComponent className="w-8 h-8 text-black" />
                    </div>
                    <h3 className="text-xl font-semibold text-white mb-4">{benefit.title}</h3>
                    <p className="text-gray-400 leading-relaxed">{benefit.description}</p>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </section>

      <section className="py-20 bg-gray-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-16 items-center">
            <div className="animate-on-scroll">
              <h2 className="text-4xl md:text-5xl font-thin text-white mb-8">
                {careers.culture.title}
              </h2>
              <p className="text-lg text-gray-300 mb-8 leading-relaxed">
                {careers.culture.description}
              </p>

              <div className="grid grid-cols-2 gap-4 mb-8">
                {careers.culture.values.map((value, index) => (
                  <div key={index} className="flex items-center space-x-3">
                    <div className="w-2 h-2 bg-gradient-to-r from-gray-400 to-yellow-500 rounded-full"></div>
                    <span className="text-gray-300 text-sm">{value}</span>
                  </div>
                ))}
              </div>

              <div className="glass-effect rounded-2xl p-6">
                <h4 className="text-lg font-semibold text-white mb-4">Employee Testimonial</h4>
                <p className="text-gray-400 italic leading-relaxed mb-4">
                  {careers.culture.testimonial.quote}
                </p>
                <div className="text-sm text-gray-500">
                  - {careers.culture.testimonial.author}, {careers.culture.testimonial.role}
                </div>
              </div>
            </div>

            <div className="animate-on-scroll">
              <img
                src={careers.culture.image}
                alt="Our culture"
                className="w-full h-96 object-cover rounded-2xl"
              />
            </div>
          </div>
        </div>
      </section>

      <section className="py-20 bg-gradient-to-b from-black to-gray-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16 animate-on-scroll">
            <h2 className="text-4xl md:text-5xl font-thin text-white mb-6">
              {careers.positions.title}
            </h2>
            <p className="text-lg text-gray-400 max-w-3xl mx-auto">
              {careers.positions.description}
            </p>
          </div>

          <div className="space-y-6">
            {careers.positions.jobs.map((position, index) => (
              <div
                key={index}
                className="glass-effect rounded-2xl p-6 hover-lift animate-on-scroll"
                style={{ animationDelay: `${index * 0.1}s` }}>
                <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                  <div className="flex-1">
                    <div className="flex flex-wrap items-center gap-4 mb-4">
                      <h3 className="text-xl font-semibold text-white">{position.title}</h3>
                      <span className="px-3 py-1 bg-gradient-to-r from-gray-400 to-yellow-500 text-black text-xs rounded-full font-medium">
                        {position.department}
                      </span>
                    </div>

                    <div className="flex flex-wrap items-center gap-4 mb-4 text-sm text-gray-400">
                      <span className="flex items-center space-x-1">
                        <MapPin size={14} />
                        <span>{position.location}</span>
                      </span>
                      <span className="flex items-center space-x-1">
                        <Clock size={14} />
                        <span>{position.type}</span>
                      </span>
                      <span className="flex items-center space-x-1">
                        <Briefcase size={14} />
                        <span>{position.experience}</span>
                      </span>
                    </div>

                    <p className="text-gray-300 mb-4 leading-relaxed">{position.description}</p>

                    <div className="mb-4">
                      <h4 className="text-sm font-semibold text-white mb-2">Key Requirements:</h4>
                      <ul className="space-y-1">
                        {position.requirements.map((req, reqIndex) => (
                          <li key={reqIndex} className="text-sm text-gray-400 flex items-center">
                            <div className="w-1.5 h-1.5 bg-gradient-to-r from-gray-400 to-yellow-500 rounded-full mr-3"></div>
                            {req}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>

                  <div className="lg:ml-8">
                    <button className="metallic-button px-6 py-3 rounded-lg font-medium flex items-center space-x-2 hover:scale-105 transition-transform duration-300">
                      <span>Apply Now</span>
                      <ArrowRight size={16} />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      <section className="py-20 bg-gradient-to-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16 animate-on-scroll">
            <h2 className="text-4xl md:text-5xl font-thin text-white mb-6">
              {careers.process.title}
            </h2>
            <p className="text-lg text-gray-400 max-w-3xl mx-auto">{careers.process.description}</p>
          </div>

          <div className="glass-effect rounded-3xl p-8 md:p-12 animate-on-scroll">
            <div className="grid md:grid-cols-4 gap-8">
              {careers.process.steps.map((process, index) => (
                <div key={index} className="text-center">
                  <div className="w-16 h-16 rounded-full bg-gradient-to-r from-gray-400 to-yellow-500 flex items-center justify-center mx-auto mb-4">
                    <span className="text-black font-bold mono-font">{process.step}</span>
                  </div>
                  <h4 className="text-lg font-semibold text-white mb-2">{process.title}</h4>
                  <p className="text-gray-400 text-sm">{process.description}</p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default CareersPage;