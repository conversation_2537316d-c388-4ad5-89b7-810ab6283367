import { Moon, Sun } from 'lucide-react';
import React, { useContext } from 'react';
import { useContent } from '../hooks/useContent';
import { ThemeContext } from '../hooks/useTheme';

interface ThemeToggleProps {
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'icon' | 'button' | 'switch';
}

const ThemeToggle: React.FC<ThemeToggleProps> = ({
  className = '',
  size = 'md',
  variant = 'icon',
}) => {
  const themeContext = useContext(ThemeContext);
  const { ui } = useContent();

  // Fallback for when ThemeProvider is not available
  const [fallbackTheme, setFallbackTheme] = React.useState<'light' | 'dark'>('light');
  const theme = themeContext?.theme || fallbackTheme;
  const toggleTheme =
    themeContext?.toggleTheme ||
    (() => {
      const newTheme = fallbackTheme === 'light' ? 'dark' : 'light';
      setFallbackTheme(newTheme);
      if (typeof document !== 'undefined') {
        document.documentElement.setAttribute('data-theme', newTheme);
      }
    });

  const sizeClasses = {
    sm: 'w-8 h-8 text-sm',
    md: 'w-10 h-10 text-base',
    lg: 'w-12 h-12 text-lg',
  };

  const iconSizes = {
    sm: 16,
    md: 20,
    lg: 24,
  };

  if (variant === 'switch') {
    return (
      <button
        onClick={toggleTheme}
        className={`relative inline-flex items-center h-6 rounded-full w-11 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2 hover:scale-105 ${theme === 'dark'
          ? 'bg-gradient-to-r from-yellow-400 to-yellow-600 focus:ring-yellow-400'
          : 'bg-gradient-to-r from-purple-400 to-purple-600 focus:ring-purple-400'
          } ${className}`}
        aria-label={theme === 'light' ? ui.theme.switchToDark : ui.theme.switchToLight}>
        <span
          className={`inline-block w-4 h-4 transform transition-all duration-300 bg-white rounded-full shadow-lg ${theme === 'dark' ? 'translate-x-6' : 'translate-x-1'
            }`}
        />
        <Sun
          size={12}
          className={`absolute left-1 text-orange-600 transition-all duration-300 ${theme === 'dark' ? 'opacity-0 scale-75' : 'opacity-100 scale-100'
            }`}
        />
        <Moon
          size={12}
          className={`absolute right-1 text-yellow-200 transition-all duration-300 ${theme === 'dark' ? 'opacity-100 scale-100' : 'opacity-0 scale-75'
            }`}
        />
      </button>
    );
  }

  if (variant === 'button') {
    return (
      <button
        onClick={toggleTheme}
        className={`inline-flex items-center gap-2 px-3 py-2 rounded-lg transition-all duration-300 hover:scale-105 focus:outline-none focus:ring-2 focus:ring-offset-2 ${theme === 'dark'
          ? 'bg-surface-secondary text-secondary hover:bg-surface-tertiary focus:ring-secondary border border-secondary/20'
          : 'bg-surface text-primary hover:bg-surface-secondary shadow-sm border border-primary/20 focus:ring-primary'
          } ${className}`}
        aria-label={theme === 'light' ? ui.theme.switchToDark : ui.theme.switchToLight}>
        {theme === 'light' ? (
          <>
            <Moon size={iconSizes[size]} className="text-primary" />
            <span className="text-sm font-medium">{ui.theme.darkMode}</span>
          </>
        ) : (
          <>
            <Sun size={iconSizes[size]} className="text-secondary" />
            <span className="text-sm font-medium">{ui.theme.lightMode}</span>
          </>
        )}
      </button>
    );
  }

  // Default icon variant
  return (
    <button
      onClick={toggleTheme}
      className={`${sizeClasses[size]} rounded-full transition-all duration-300 hover:scale-110 focus:outline-none focus:ring-2 focus:ring-offset-2 ${theme === 'dark'
        ? 'bg-surface-secondary text-secondary hover:bg-surface-tertiary focus:ring-secondary border border-secondary/20 shadow-lg'
        : 'bg-surface text-primary hover:bg-surface-secondary shadow-lg border border-primary/20 focus:ring-primary'
        } flex items-center justify-center ${className}`}
      aria-label={theme === 'light' ? ui.theme.switchToDark : ui.theme.switchToLight}>
      {theme === 'light' ? (
        <Moon size={iconSizes[size]} className="text-primary" />
      ) : (
        <Sun size={iconSizes[size]} className="text-secondary" />
      )}
    </button>
  );
};

export default ThemeToggle;
