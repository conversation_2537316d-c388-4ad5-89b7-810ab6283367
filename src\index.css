@import url("https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&family=JetBrains+Mono:wght@300;400;500;600&display=swap");

@tailwind base;
@tailwind components;
@tailwind utilities;

*,
*::before,
*::after {
  box-sizing: border-box;
}
html {
  scroll-behavior: smooth;
  font-size: 16px;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}
body {
  font-family: var(--font-primary);
  margin: 0;
  color: var(--color-text-primary);
  background: var(--color-background-primary);
  line-height: 1.6;
  transition: all var(--transition-theme);
  overflow-x: hidden;
}
:root {
  --brand-purple-50: #f8f6ff;
  --brand-purple-100: #f0ebff;
  --brand-purple-200: #e4d9ff;
  --brand-purple-300: #d1bfff;
  --brand-purple-400: #b899ff;
  --brand-purple-500: #9c6eff;
  --brand-purple-600: #7916ff;
  --brand-purple-700: #6b04fd;
  --brand-purple-800: #5a03d4;
  --brand-purple-900: #4c05ad;
  --brand-purple-950: #2e0276;
  --brand-orange-50: #fff8ed;
  --brand-orange-100: #ffedd5;
  --brand-orange-200: #fed7aa;
  --brand-orange-300: #fdba74;
  --brand-orange-400: #fb923c;
  --brand-orange-500: #f97316;
  --brand-orange-600: #ea580c;
  --brand-orange-700: #c2410c;
  --brand-orange-800: #9a3412;
  --brand-orange-900: #7c2d12;
  --brand-orange-950: #431407;
  --color-primary: var(--brand-purple-600);
  --color-primary-hover: var(--brand-purple-700);
  --color-primary-light: var(--brand-purple-100);
  --color-primary-dark: var(--brand-purple-800);
  --color-secondary: var(--brand-orange-500);
  --color-secondary-hover: var(--brand-orange-600);
  --color-secondary-light: var(--brand-orange-100);
  --color-secondary-dark: var(--brand-orange-700);
  --color-accent: var(--brand-purple-500);
  --color-accent-hover: var(--brand-purple-600);
  --color-background-primary: #ffffff;
  --color-background-secondary: #fafbfc;
  --color-background-tertiary: var(--brand-purple-50);
  --color-background-elevated: #ffffff;
  --color-surface-primary: #ffffff;
  --color-surface-secondary: #f8fafc;
  --color-surface-tertiary: #f1f5f9;
  --color-surface-elevated: #ffffff;
  --color-text-primary: #0f172a;
  --color-text-secondary: #475569;
  --color-text-tertiary: #64748b;
  --color-text-inverse: #ffffff;
  --color-text-muted: #94a3b8;
  --color-border-primary: #e2e8f0;
  --color-border-secondary: #cbd5e1;
  --color-border-tertiary: #94a3b8;
  --color-border-focus: var(--color-primary);
  --color-success: #10b981;
  --color-warning: #f59e0b;
  --color-error: #ef4444;
  --color-info: var(--color-primary);
  --color-luxury-platinum: #e5e4e2;
  --color-luxury-gold: #d4af37;
  --color-luxury-silver: #c0c0c0;
  --color-luxury-bronze: #cd7f32;
  --color-glass-background: rgba(255, 255, 255, 0.85);
  --color-glass-border: rgba(121, 22, 255, 0.12);
  --color-glass-backdrop: blur(20px);
  --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --shadow-luxury: 0 32px 64px -12px rgba(121, 22, 255, 0.15);
  --font-primary: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  --font-display: "Inter", serif;
  --font-mono: "JetBrains Mono", "Fira Code", monospace;
  --space-px: 1px;
  --space-0: 0;
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-5: 1.25rem;
  --space-6: 1.5rem;
  --space-8: 2rem;
  --space-10: 2.5rem;
  --space-12: 3rem;
  --space-16: 4rem;
  --space-20: 5rem;
  --space-24: 6rem;
  --space-32: 8rem;
  --radius-none: 0;
  --radius-sm: 0.125rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  --radius-2xl: 1rem;
  --radius-3xl: 1.5rem;
  --radius-full: 9999px;
  --transition-theme: all 300ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-fast: all 150ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-base: all 200ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: all 300ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-luxury: all 500ms cubic-bezier(0.25, 0.46, 0.45, 0.94);
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toast: 1080;
}
[data-theme="dark"] {
  --color-primary: var(--brand-purple-400);
  --color-primary-hover: var(--brand-purple-300);
  --color-primary-light: var(--brand-purple-900);
  --color-primary-dark: var(--brand-purple-200);
  --color-secondary: var(--brand-orange-400);
  --color-secondary-hover: var(--brand-orange-300);
  --color-secondary-light: var(--brand-orange-900);
  --color-secondary-dark: var(--brand-orange-200);
  --color-accent: var(--brand-purple-300);
  --color-accent-hover: var(--brand-purple-200);
  --color-background-primary: #0a0a0a;
  --color-background-secondary: #111111;
  --color-background-tertiary: #1a1a1a;
  --color-background-elevated: #1f1f1f;
  --color-surface-primary: #1a1a1a;
  --color-surface-secondary: #222222;
  --color-surface-tertiary: #2a2a2a;
  --color-surface-elevated: #2f2f2f;
  --color-text-primary: #f8fafc;
  --color-text-secondary: #e2e8f0;
  --color-text-tertiary: #cbd5e1;
  --color-text-inverse: #0f172a;
  --color-text-muted: #64748b;
  --color-border-primary: #374151;
  --color-border-secondary: #4b5563;
  --color-border-tertiary: #6b7280;
  --color-border-focus: var(--color-primary);
  --color-success: #34d399;
  --color-warning: #fbbf24;
  --color-error: #f87171;
  --color-info: var(--color-primary);
  --color-luxury-platinum: #f1f5f9;
  --color-luxury-gold: #fbbf24;
  --color-luxury-silver: #e5e7eb;
  --color-luxury-bronze: #f59e0b;
  --color-glass-background: rgba(0, 0, 0, 0.6);
  --color-glass-border: rgba(156, 110, 255, 0.2);
  --color-glass-backdrop: blur(20px);
  --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.4), 0 1px 2px -1px rgba(0, 0, 0, 0.4);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4), 0 2px 4px -2px rgba(0, 0, 0, 0.4);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.4), 0 4px 6px -4px rgba(0, 0, 0, 0.4);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.5), 0 8px 10px -6px rgba(0, 0, 0, 0.5);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.6);
  --shadow-luxury: 0 32px 64px -12px rgba(156, 110, 255, 0.3);
}
.container-responsive {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 var(--space-4);
  width: 100%;
}
@media (min-width: 640px) {
  .container-responsive {
    padding: 0 var(--space-6);
  }
}
@media (min-width: 1024px) {
  .container-responsive {
    padding: 0 var(--space-8);
  }
}
.heading-display {
  font-family: var(--font-display);
  font-weight: 300;
  line-height: 1.1;
  letter-spacing: -0.02em;
  background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  font-size: clamp(2.5rem, 5vw, 4rem);
  text-rendering: optimizeLegibility;
}
.heading-primary {
  font-family: var(--font-primary);
  font-size: clamp(2rem, 4vw, 3rem);
  font-weight: 400;
  color: var(--color-text-primary);
  line-height: 1.2;
  letter-spacing: -0.01em;
}
.heading-secondary {
  font-family: var(--font-primary);
  font-size: clamp(1.5rem, 3vw, 2rem);
  font-weight: 500;
  color: var(--color-text-primary);
  line-height: 1.3;
}
.heading-tertiary {
  font-family: var(--font-primary);
  font-size: clamp(1.25rem, 2.5vw, 1.5rem);
  font-weight: 600;
  color: var(--color-text-primary);
  line-height: 1.4;
}
.text-body-large {
  font-family: var(--font-primary);
  font-size: 1.125rem;
  color: var(--color-text-secondary);
  line-height: 1.6;
  font-weight: 300;
}
.text-body {
  font-family: var(--font-primary);
  font-size: 1rem;
  color: var(--color-text-secondary);
  line-height: 1.6;
  font-weight: 400;
}
.text-small {
  font-family: var(--font-primary);
  font-size: 0.875rem;
  color: var(--color-text-tertiary);
  line-height: 1.5;
}
.text-caption {
  font-family: var(--font-mono);
  font-size: 0.75rem;
  color: var(--color-text-muted);
  line-height: 1.4;
  letter-spacing: 0.05em;
  text-transform: uppercase;
  font-weight: 500;
}
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-3) var(--space-5);
  border-radius: var(--radius-lg);
  font-family: var(--font-primary);
  font-weight: 600;
  font-size: 0.95rem;
  letter-spacing: 0.01em;
  transition: var(--transition-base);
  cursor: pointer;
  border: none;
  position: relative;
  overflow: hidden;
  text-decoration: none;
  user-select: none;
}
.btn:focus-visible {
  outline: 2px solid var(--color-border-focus);
  outline-offset: 2px;
}
.btn-primary {
  background: linear-gradient(135deg, var(--color-primary), var(--color-primary-hover));
  color: var(--color-text-inverse);
  box-shadow: var(--shadow-md);
}
.btn-primary:hover {
  background: linear-gradient(135deg, var(--color-primary-hover), var(--color-primary-dark));
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}
.btn-primary:active {
  transform: translateY(0);
  box-shadow: var(--shadow-sm);
}
.btn-secondary {
  background: linear-gradient(135deg, var(--color-secondary), var(--color-secondary-hover));
  color: var(--color-text-inverse);
  box-shadow: var(--shadow-md);
}
.btn-secondary:hover {
  background: linear-gradient(135deg, var(--color-secondary-hover), var(--color-secondary-dark));
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}
.btn-ghost {
  background: transparent;
  border: 1px solid var(--color-border-secondary);
  color: var(--color-text-secondary);
}
.btn-ghost:hover {
  background: var(--color-surface-secondary);
  border-color: var(--color-border-primary);
  color: var(--color-text-primary);
}
.btn-luxury {
  background: linear-gradient(135deg, var(--color-luxury-platinum), var(--color-luxury-silver));
  color: var(--color-text-primary);
  border: 1px solid var(--color-luxury-gold);
  position: relative;
  overflow: hidden;
}
.btn-luxury::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left var(--transition-luxury);
}
.btn-luxury:hover::before {
  left: 100%;
}
.btn-luxury:hover {
  background: linear-gradient(135deg, var(--color-luxury-gold), var(--color-luxury-bronze));
  transform: translateY(-2px);
  box-shadow: var(--shadow-luxury);
}
.card {
  background: var(--color-surface-primary);
  border-radius: var(--radius-2xl);
  padding: var(--space-6);
  border: 1px solid var(--color-border-primary);
  box-shadow: var(--shadow-md);
  transition: var(--transition-base);
}
.card:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}
.card-elevated {
  background: var(--color-surface-elevated);
  box-shadow: var(--shadow-xl);
}
.card-luxury {
  background: var(--color-surface-primary);
  border: 1px solid var(--color-luxury-gold);
  box-shadow: var(--shadow-luxury);
  position: relative;
  overflow: hidden;
}
.card-luxury::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, var(--color-primary), var(--color-secondary));
}
.glass-effect {
  background: var(--color-glass-background);
  border: 1px solid var(--color-glass-border);
  backdrop-filter: var(--color-glass-backdrop);
  border-radius: var(--radius-2xl);
  padding: var(--space-4);
}
.glass-card {
  background: var(--color-glass-background);
  border: 1px solid var(--color-glass-border);
  backdrop-filter: var(--color-glass-backdrop);
  border-radius: var(--radius-2xl);
  padding: var(--space-6);
  box-shadow: var(--shadow-lg);
}
.gradient-primary {
  background: linear-gradient(135deg, var(--color-primary), var(--color-primary-hover));
}
.gradient-secondary {
  background: linear-gradient(135deg, var(--color-secondary), var(--color-secondary-hover));
}
.gradient-brand {
  background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
}
.gradient-luxury {
  background: linear-gradient(135deg, var(--color-luxury-gold), var(--color-luxury-bronze));
}
.text-gradient-brand {
  background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}
.text-gradient-luxury {
  background: linear-gradient(135deg, var(--color-luxury-gold), var(--color-luxury-platinum));
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}
.pattern-grid {
  background-image:
    linear-gradient(var(--color-border-primary) 1px, transparent 1px),
    linear-gradient(90deg, var(--color-border-primary) 1px, transparent 1px);
  background-size: 50px 50px;
  opacity: 0.5;
}
.pattern-dots {
  background-image: radial-gradient(var(--color-border-primary) 1px, transparent 1px);
  background-size: 20px 20px;
  opacity: 0.3;
}
@keyframes float {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-8px);
  }
}
@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}
@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}
@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}
.animate-float {
  animation: float 6s ease-in-out infinite;
}
.animate-shimmer {
  background: linear-gradient(90deg, transparent, var(--color-text-muted), transparent);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}
.animate-fade-in-up {
  animation: fadeInUp var(--transition-luxury) ease-out;
}
.animate-slide-in-left {
  animation: slideInLeft var(--transition-slow) ease-out;
}
.animate-slide-in-right {
  animation: slideInRight var(--transition-slow) ease-out;
}
.animate-scale-in {
  animation: scaleIn var(--transition-slow) ease-out;
}
.hover-lift {
  transition: var(--transition-base);
}
.hover-lift:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-2xl);
}
.hover-glow {
  transition: var(--transition-base);
}
.hover-glow:hover {
  box-shadow: 0 0 30px var(--color-primary);
  transform: translateY(-2px);
}
.hover-scale {
  transition: var(--transition-base);
}
.hover-scale:hover {
  transform: scale(1.05);
}
.bg-primary {
  background-color: var(--color-background-primary);
}
.bg-secondary {
  background-color: var(--color-background-secondary);
}
.bg-surface {
  background-color: var(--color-surface-primary);
}
.bg-elevated {
  background-color: var(--color-surface-elevated);
}
.text-primary {
  color: var(--color-text-primary);
}
.text-secondary {
  color: var(--color-text-secondary);
}
.text-tertiary {
  color: var(--color-text-tertiary);
}
.text-muted {
  color: var(--color-text-muted);
}
.text-inverse {
  color: var(--color-text-inverse);
}
.border-primary {
  border-color: var(--color-border-primary);
}
.border-secondary {
  border-color: var(--color-border-secondary);
}
.border-focus {
  border-color: var(--color-border-focus);
}
:focus-visible {
  outline: 2px solid var(--color-border-focus);
  outline-offset: 2px;
}
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0 0 0 0);
  white-space: nowrap;
  border: 0;
}
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}
@media print {
  * {
    box-shadow: none !important;
    text-shadow: none !important;
  }
  a[href]:after {
    content: " (" attr(href) ")";
  }
  .no-print {
    display: none !important;
  }
}
@media (prefers-contrast: high) {
  :root {
    --color-border-primary: #000000;
    --color-text-primary: #000000;
    --color-background-primary: #ffffff;
  }
  [data-theme="dark"] {
    --color-border-primary: #ffffff;
    --color-text-primary: #ffffff;
    --color-background-primary: #000000;
  }
}
.luxury-heading-display {
  font-family: var(--font-display);
  font-weight: 200;
  letter-spacing: -0.03em;
  line-height: 0.9;
  text-rendering: optimizeLegibility;
  background: linear-gradient(
    135deg,
    var(--color-primary),
    var(--color-secondary),
    var(--color-luxury-gold)
  );
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  font-size: clamp(3rem, 6vw, 5rem);
  position: relative;
}
.luxury-heading-display::after {
  content: "";
  position: absolute;
  bottom: -10px;
  left: 0;
  width: 60px;
  height: 3px;
  background: linear-gradient(90deg, var(--color-primary), var(--color-secondary));
  border-radius: var(--radius-full);
}
.luxury-heading-primary {
  font-family: var(--font-primary);
  font-weight: 300;
  letter-spacing: -0.02em;
  line-height: 1.1;
  color: var(--color-text-primary);
  position: relative;
}
.luxury-heading-secondary {
  font-family: var(--font-primary);
  font-weight: 400;
  letter-spacing: -0.01em;
  line-height: 1.2;
  color: var(--color-text-primary);
}
.luxury-body-large {
  font-family: var(--font-primary);
  font-weight: 300;
  font-size: 1.25rem;
  line-height: 1.7;
  letter-spacing: 0.01em;
  color: var(--color-text-secondary);
}
.luxury-caption {
  font-family: var(--font-mono);
  font-weight: 500;
  font-size: 0.75rem;
  line-height: 1.4;
  letter-spacing: 0.1em;
  text-transform: uppercase;
  color: var(--color-text-muted);
}
.luxury-gradient-platinum {
  background: linear-gradient(
    135deg,
    var(--color-luxury-platinum),
    var(--color-luxury-silver),
    #f8f8ff,
    #e5e4e2
  );
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}
.luxury-gradient-gold {
  background: linear-gradient(
    135deg,
    var(--color-luxury-gold),
    #ffed4e,
    #d4af37,
    var(--color-luxury-bronze)
  );
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}
.luxury-gradient-brand {
  background: linear-gradient(
    135deg,
    var(--color-primary),
    var(--color-secondary),
    var(--color-luxury-gold)
  );
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}
.luxury-button-primary {
  padding: var(--space-4) var(--space-6);
  font-size: 0.95rem;
  font-weight: 600;
  letter-spacing: 0.02em;
  position: relative;
  transition: var(--transition-luxury);
  overflow: hidden;
  background: linear-gradient(135deg, var(--color-luxury-platinum), var(--color-luxury-silver));
  color: var(--color-text-primary);
  border: 1px solid var(--color-luxury-gold);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
}
.luxury-button-primary::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left var(--transition-luxury);
}
.luxury-button-primary:hover::before {
  left: 100%;
}
.luxury-button-primary:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-luxury);
  background: linear-gradient(135deg, var(--color-luxury-gold), var(--color-luxury-bronze));
  border-color: var(--color-primary);
}
.luxury-button-secondary {
  padding: var(--space-4) var(--space-6);
  font-size: 0.95rem;
  font-weight: 500;
  letter-spacing: 0.02em;
  position: relative;
  transition: var(--transition-luxury);
  overflow: hidden;
  background: transparent;
  color: var(--color-text-primary);
  border: 1px solid var(--color-border-secondary);
  border-radius: var(--radius-xl);
}
.luxury-button-secondary::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 0;
  height: 100%;
  background: var(--color-surface-secondary);
  transition: width var(--transition-slow);
  z-index: -1;
}
.luxury-button-secondary:hover::before {
  width: 100%;
}
.luxury-button-secondary:hover {
  border-color: var(--color-primary);
  color: var(--color-primary);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}
.luxury-glass-primary {
  background: var(--color-glass-background);
  backdrop-filter: blur(20px) saturate(180%);
  border: 1px solid var(--color-glass-border);
  box-shadow: var(--shadow-lg);
  border-radius: var(--radius-2xl);
  padding: var(--space-6);
}
.luxury-glass-secondary {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(16px) saturate(160%);
  border: 1px solid var(--color-border-primary);
  box-shadow: var(--shadow-md);
  border-radius: var(--radius-xl);
  padding: var(--space-4);
}
[data-theme="dark"] .luxury-glass-secondary {
  background: rgba(0, 0, 0, 0.3);
  border-color: var(--color-border-secondary);
}
.luxury-glass-accent {
  background: rgba(121, 22, 255, 0.08);
  backdrop-filter: blur(24px) saturate(200%);
  border: 1px solid var(--color-primary);
  box-shadow: var(--shadow-luxury);
  border-radius: var(--radius-2xl);
  padding: var(--space-6);
}
[data-theme="dark"] .luxury-glass-accent {
  background: rgba(156, 110, 255, 0.12);
  border-color: var(--color-primary);
}
@keyframes luxuryFadeInUp {
  from {
    opacity: 0;
    transform: translateY(40px) scale(0.98);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}
@keyframes luxurySlideInLeft {
  from {
    opacity: 0;
    transform: translateX(-60px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}
@keyframes luxurySlideInRight {
  from {
    opacity: 0;
    transform: translateX(60px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}
@keyframes luxuryScaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}
@keyframes luxuryFloat {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-12px);
  }
}
@keyframes luxuryShimmer {
  from {
    background-position: -200% 0;
  }
  to {
    background-position: 200% 0;
  }
}
@keyframes luxuryPulse {
  0%,
  100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}
@keyframes luxuryGlow {
  0%,
  100% {
    box-shadow: 0 0 20px var(--color-primary);
  }
  50% {
    box-shadow:
      0 0 40px var(--color-primary),
      0 0 60px var(--color-secondary);
  }
}
.luxury-animate-fade-up {
  animation: luxuryFadeInUp var(--transition-luxury) ease-out forwards;
}
.luxury-animate-slide-left {
  animation: luxurySlideInLeft var(--transition-slow) ease-out forwards;
}
.luxury-animate-slide-right {
  animation: luxurySlideInRight var(--transition-slow) ease-out forwards;
}
.luxury-animate-scale {
  animation: luxuryScaleIn var(--transition-slow) ease-out forwards;
}
.luxury-animate-float {
  animation: luxuryFloat 4s ease-in-out infinite;
}
.luxury-animate-pulse {
  animation: luxuryPulse 2s ease-in-out infinite;
}
.luxury-animate-glow {
  animation: luxuryGlow 3s ease-in-out infinite;
}
.luxury-shimmer {
  background: linear-gradient(90deg, transparent, var(--color-text-muted), transparent);
  background-size: 200% 100%;
  animation: luxuryShimmer 2s infinite;
}
.luxury-hover-lift {
  transition: var(--transition-luxury);
}
.luxury-hover-lift:hover {
  transform: translateY(-12px) scale(1.02);
  box-shadow: var(--shadow-luxury);
}
.luxury-hover-glow {
  transition: var(--transition-luxury);
  position: relative;
}
.luxury-hover-glow::before {
  content: "";
  position: absolute;
  top: -3px;
  left: -3px;
  right: -3px;
  bottom: -3px;
  background: linear-gradient(45deg, var(--color-primary), var(--color-secondary));
  border-radius: inherit;
  opacity: 0;
  transition: opacity var(--transition-luxury);
  z-index: -1;
  filter: blur(10px);
}
.luxury-hover-glow:hover::before {
  opacity: 0.4;
}
.luxury-hover-scale {
  transition: var(--transition-base);
}
.luxury-hover-scale:hover {
  transform: scale(1.05);
}
.luxury-hover-tilt {
  transition: var(--transition-luxury);
  transform-style: preserve-3d;
}
.luxury-hover-tilt:hover {
  transform: perspective(1000px) rotateX(10deg) rotateY(10deg);
}
.luxury-card {
  background: var(--color-surface-primary);
  border: 1px solid var(--color-border-primary);
  border-radius: var(--radius-2xl);
  padding: var(--space-6);
  box-shadow: var(--shadow-lg);
  transition: var(--transition-luxury);
  position: relative;
  overflow: hidden;
}
.luxury-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, var(--color-primary), var(--color-secondary));
  opacity: 0;
  transition: opacity var(--transition-base);
}
.luxury-card:hover::before {
  opacity: 1;
}
.luxury-card:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-luxury);
  border-color: var(--color-primary);
}
.luxury-card-elevated {
  background: var(--color-surface-elevated);
  box-shadow: var(--shadow-2xl);
}
.luxury-card-glass {
  background: var(--color-glass-background);
  backdrop-filter: var(--color-glass-backdrop);
  border: 1px solid var(--color-glass-border);
}
.luxury-grid-masonry {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--space-6);
}
.luxury-grid-featured {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr;
  grid-template-rows: 1fr 1fr;
  gap: var(--space-6);
  height: 600px;
}
.luxury-grid-featured .featured-main {
  grid-row: 1 / 3;
}
@media (max-width: 1200px) {
  .luxury-grid-featured {
    grid-template-columns: 1fr 1fr;
    grid-template-rows: 2fr 1fr 1fr;
  }
  .luxury-grid-featured .featured-main {
    grid-column: 1 / 3;
    grid-row: 1;
  }
}
@media (max-width: 768px) {
  .luxury-grid-featured {
    grid-template-columns: 1fr;
    grid-template-rows: auto;
    height: auto;
  }
  .luxury-grid-featured .featured-main {
    grid-column: 1;
    grid-row: auto;
  }
  .luxury-heading-display {
    font-size: clamp(2.5rem, 8vw, 4rem);
  }
  .luxury-body-large {
    font-size: 1rem;
  }
}
@media (prefers-reduced-motion: reduce) {
  .luxury-animate-fade-up,
  .luxury-animate-slide-left,
  .luxury-animate-slide-right,
  .luxury-animate-scale,
  .luxury-animate-float,
  .luxury-animate-pulse,
  .luxury-animate-glow,
  .luxury-shimmer {
    animation: none !important;
  }
  .luxury-hover-lift:hover,
  .luxury-hover-scale:hover,
  .luxury-hover-tilt:hover {
    transform: none !important;
  }
}
.custom-cursor {
  position: fixed;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: radial-gradient(circle, var(--color-primary) 0%, transparent 70%);
  border: 2px solid var(--color-primary);
  transition: var(--transition-fast);
  transform-origin: center;
  pointer-events: none;
  z-index: 9999;
  mix-blend-mode: difference;
}
.custom-cursor.hovering {
  transform: scale(1.5);
  background: radial-gradient(circle, var(--color-secondary) 0%, transparent 70%);
  border-color: var(--color-secondary);
  box-shadow: 0 0 20px var(--color-secondary);
}
.custom-cursor.clicking {
  transform: scale(0.8);
  background: radial-gradient(circle, var(--color-luxury-gold) 0%, transparent 70%);
  border-color: var(--color-luxury-gold);
  box-shadow: 0 0 15px var(--color-luxury-gold);
}
.custom-cursor-active * {
  cursor: none !important;
}
.luxury-scroll-indicator {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background: linear-gradient(90deg, var(--color-primary), var(--color-secondary));
  transform-origin: left;
  z-index: var(--z-fixed);
  transition: var(--transition-base);
}
::selection {
  background: var(--color-primary);
  color: var(--color-text-inverse);
}
::-moz-selection {
  background: var(--color-primary);
  color: var(--color-text-inverse);
}
::-webkit-scrollbar {
  width: 8px;
}
::-webkit-scrollbar-track {
  background: var(--color-background-secondary);
}
::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, var(--color-primary), var(--color-secondary));
  border-radius: 4px;
}
::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, var(--color-secondary), var(--color-primary));
}
