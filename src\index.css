@import url("https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&family=JetBrains+Mono:wght@300;400;500;600&display=swap");
@import "./styles/premium-enhancements.css";

@tailwind base;
@tailwind components;
@tailwind utilities;

*,
:where(*::before, *::after) {
  box-sizing: border-box;
}
html {
  scroll-behavior: smooth;
  font-size: 16px;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
body {
  font-family:
    Inter,
    system-ui,
    -apple-system,
    BlinkMacSystemFont,
    "Segoe UI",
    Roboto,
    Helvetica,
    Arial,
    sans-serif;
  margin: 0;
  color: var(--text-primary);
  background: var(--bg-primary);
  line-height: 1.5;
  transition:
    background-color 0.3s,
    color 0.3s;
}
:root {
  --accent-purple: #7916ff;
  --accent-orange: #f97316;
  --gold: #d4af37;
  --gold-light: #f4d03f;
  --bg-primary: #ffffff;
  --bg-secondary: #faf9ff;
  --surface: #ffffff;
  --text-primary: #1f1f23;
  --text-secondary: #6b6b7a;
  --border: #e5e7eb;
  --glass-bg: rgba(255, 255, 255, 0.85);
  --glass-border: rgba(121, 22, 255, 0.12);
  --shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --radius-lg: 0.5rem;
  --transition-base: 300ms cubic-bezier(0.4, 0, 0.2, 1);
}
[data-theme="dark"] {
  --bg-primary: #0a0a0a;
  --bg-secondary: #1a1a1a;
  --surface: #1a1a1a;
  --text-primary: #f9fafb;
  --text-secondary: #d1d5db;
  --border: #374151;
  --glass-bg: rgba(0, 0, 0, 0.6);
  --glass-border: rgba(212, 175, 55, 0.18);
  --accent-purple: var(--gold);
}
.container-responsive {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 1rem;
}
@media (min-width: 640px) {
  .container-responsive {
    padding: 0 1.5rem;
  }
}
@media (min-width: 1024px) {
  .container-responsive {
    padding: 0 2rem;
  }
}
.heading-display {
  font-family: Inter, system-ui;
  font-weight: 300;
  line-height: 1.1;
  letter-spacing: -0.02em;
  background: linear-gradient(135deg, var(--accent-purple), var(--accent-orange));
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  font-size: clamp(2.5rem, 5vw, 4rem);
}
.heading-primary {
  font-size: clamp(2rem, 4vw, 3rem);
  font-weight: 400;
  color: var(--text-primary);
}
.heading-secondary {
  font-size: clamp(1.5rem, 3vw, 2rem);
  font-weight: 500;
  color: var(--text-primary);
}
.text-body-large {
  font-size: 1.125rem;
  color: var(--text-secondary);
  line-height: 1.6;
}
.text-small {
  font-size: 0.875rem;
  color: var(--text-secondary);
}
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1.25rem;
  border-radius: var(--radius-lg);
  font-weight: 600;
  transition: all var(--transition-base);
  cursor: pointer;
  border: none;
}
.btn-primary {
  background: linear-gradient(135deg, var(--gold), var(--gold-light));
  color: #0a0a0a;
}
.btn-ghost {
  background: transparent;
  border: 1px solid transparent;
  color: var(--text-secondary);
}
.metallic-button {
  background: linear-gradient(135deg, #cfcfcf, #e8e8e8, #cfcfcf);
  color: var(--text-primary);
  font-weight: 600;
  position: relative;
  overflow: hidden;
}
.metallic-button::before {
  content: "";
  position: absolute;
  inset: 0;
  left: -100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.35), transparent);
  transition: left 0.6s;
}
.metallic-button:hover::before {
  left: 100%;
}
.card {
  background: var(--surface);
  border-radius: 1rem;
  padding: 1.25rem;
  border: 1px solid var(--border);
  box-shadow: var(--shadow);
}
.glass-effect {
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  backdrop-filter: blur(10px);
  border-radius: 1rem;
  padding: 1rem;
}
.metallic-gradient {
  background: linear-gradient(135deg, var(--gold), #e5e4e2);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}
.text-gradient {
  background: linear-gradient(135deg, #cfcfcf, var(--gold));
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}
.grid-pattern {
  background-image:
    linear-gradient(rgba(121, 22, 255, 0.02) 1px, transparent 1px),
    linear-gradient(90deg, rgba(121, 22, 255, 0.02) 1px, transparent 1px);
  background-size: 50px 50px;
}
@keyframes float {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-6px);
  }
}
.animate-float {
  animation: 6s ease-in-out infinite float;
}
@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}
.animate-shimmer {
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.08), transparent);
  background-size: 200% 100%;
  animation: 2s infinite shimmer;
}
.hover-lift {
  transition:
    transform var(--transition-base),
    box-shadow var(--transition-base);
}
.hover-lift:hover {
  transform: translateY(-8px);
  box-shadow: 0 40px 80px -24px rgba(0, 0, 0, 0.35);
}
.hover-glow {
  transition: all var(--transition-base);
}
.hover-glow:hover {
  box-shadow: 0 0 30px rgba(121, 22, 255, 0.18);
  transform: translateY(-2px);
}
:focus {
  outline: 2px solid var(--accent-purple);
  outline-offset: 2px;
}
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0 0 0 0);
  white-space: nowrap;
  border: 0;
}
@media (prefers-reduced-motion: reduce) {
  *,
  :where(*::before, *::after) {
    animation-duration: 0s !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0s !important;
    scroll-behavior: auto !important;
  }
}
@media print {
  * {
    box-shadow: none !important;
    text-shadow: none !important;
  }
  a[href]:after {
    content: " (" attr(href) ")";
  }
}
.bg-theme-primary {
  background: var(--bg-primary);
}
.text-theme-primary {
  color: var(--text-primary);
}
