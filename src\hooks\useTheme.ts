import { createContext, useContext, useEffect, useState } from 'react';

export type Theme = 'light' | 'dark';

interface ThemeContextType {
  theme: Theme;
  toggleTheme: () => void;
  setTheme: (theme: Theme) => void;
}

// Create and export the context
export const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

// Hook to consume the theme context
export const useTheme = () => {
  const context = useContext(ThemeContext);
  console.log(context);

  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

// Hook for the provider logic (to be used in ThemeProvider component)
export const useThemeProvider = () => {
  const [theme, setThemeState] = useState<Theme>(() => {
    // Check localStorage first
    if (typeof window !== 'undefined') {
      try {
        const stored = localStorage.getItem('winsastra-theme') as Theme;
        if (stored && (stored === 'light' || stored === 'dark')) {
          return stored;
        }
      } catch (error) {
        console.warn('Failed to read theme from localStorage:', error);
      }
    }

    // Check system preference
    if (
      typeof window !== 'undefined' &&
      window.matchMedia &&
      window.matchMedia('(prefers-color-scheme: dark)').matches
    ) {
      return 'dark';
    }

    // Default to light mode as per user preference
    return 'light';
  });

  const setTheme = (newTheme: Theme) => {
    setThemeState(newTheme);

    // Save to localStorage with error handling
    if (typeof window !== 'undefined') {
      try {
        localStorage.setItem('winsastra-theme', newTheme);
      } catch (error) {
        console.warn('Failed to save theme to localStorage:', error);
      }
    }

    // Apply theme to document
    if (typeof document !== 'undefined') {
      document.documentElement.setAttribute('data-theme', newTheme);
      // Add smooth transition class
      document.documentElement.classList.add('theme-transitioning');
      setTimeout(() => {
        document.documentElement.classList.remove('theme-transitioning');
      }, 300);
    }
  };

  const toggleTheme = () => {
    setTheme(theme === 'light' ? 'dark' : 'light');
  };

  useEffect(() => {
    // Set initial theme
    if (typeof document !== 'undefined') {
      document.documentElement.setAttribute('data-theme', theme);
    }

    // Listen for system theme changes
    if (typeof window !== 'undefined') {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
      const handleChange = (e: MediaQueryListEvent) => {
        // Only auto-switch if user hasn't manually set a preference
        try {
          const hasStoredPreference = localStorage.getItem('winsastra-theme');
          if (!hasStoredPreference) {
            setTheme(e.matches ? 'dark' : 'light');
          }
        } catch (error) {
          // If localStorage is not available, still respond to system changes
          setTheme(e.matches ? 'dark' : 'light');
        }
      };

      mediaQuery.addEventListener('change', handleChange);
      return () => mediaQuery.removeEventListener('change', handleChange);
    }
  }, [theme]);

  return {
    theme,
    toggleTheme,
    setTheme,
  };
};
