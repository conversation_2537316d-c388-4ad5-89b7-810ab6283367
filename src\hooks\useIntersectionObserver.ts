import { useEffect, useRef } from 'react';

interface UseIntersectionObserverOptions {
  threshold?: number | number[];
  rootMargin?: string;
  root?: Element | null;
  onIntersect?: (entry: IntersectionObserverEntry) => void;
  once?: boolean;
}

export const useIntersectionObserver = (options: UseIntersectionObserverOptions = {}) => {
  const {
    threshold = 0.1,
    rootMargin = '0px 0px -50px 0px',
    root = null,
    onIntersect,
    once = false,
  } = options;

  const elementRef = useRef<HTMLElement>(null);

  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;

    const observer = new IntersectionObserver(
      entries => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            onIntersect?.(entry);

            if (once) {
              observer.unobserve(element);
            }
          }
        });
      },
      { threshold, rootMargin, root }
    );

    observer.observe(element);

    return () => {
      observer.disconnect();
    };
  }, [threshold, rootMargin, root, onIntersect, once]);

  return elementRef;
};

export const useAnimateOnScroll = (
  animationClass: string = 'in-view',
  options: UseIntersectionObserverOptions = {}
) => {
  const onIntersect = (entry: IntersectionObserverEntry) => {
    entry.target.classList.add(animationClass);

    // Add staggered animation for child elements
    const children = entry.target.querySelectorAll('.stagger-child');
    children.forEach((child, index) => {
      setTimeout(() => {
        child.classList.add('animate-fade-in');
      }, index * 100);
    });
  };

  return useIntersectionObserver({
    ...options,
    onIntersect,
    once: true,
  });
};

// Global scroll animation initializer
export const initializePageAnimations = () => {
  const observerOptions = {
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px',
  };

  const observer = new IntersectionObserver(entries => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        entry.target.classList.add('in-view');

        // Add staggered animation for child elements
        const children = entry.target.querySelectorAll('.stagger-child');
        children.forEach((child, index) => {
          setTimeout(() => {
            child.classList.add('animate-fade-in');
          }, index * 100);
        });
      }
    });
  }, observerOptions);

  // Observe all elements with animate-on-scroll class
  const animateElements = document.querySelectorAll('.animate-on-scroll');
  animateElements.forEach(el => observer.observe(el));

  return observer;
};

export const useLazyLoad = (options: UseIntersectionObserverOptions = {}) => {
  const onIntersect = (entry: IntersectionObserverEntry) => {
    const img = entry.target as HTMLImageElement;
    if (img.dataset.src) {
      img.src = img.dataset.src;
      img.classList.remove('lazy');
    }
  };

  return useIntersectionObserver({
    ...options,
    onIntersect,
    once: true,
  });
};
