import React, { useEffect, useRef, useState } from 'react';

interface CursorState {
  x: number;
  y: number;
  isHovering: boolean;
  isClicking: boolean;
  cursorType: 'default' | 'pointer' | 'text' | 'precision' | 'industrial';
}

const CustomCursor: React.FC = () => {
  const cursorRef = useRef<HTMLDivElement>(null);
  const trailRef = useRef<HTMLDivElement>(null);
  const [isVisible, setIsVisible] = useState(false);
  const [cursorState, setCursorState] = useState<CursorState>({
    x: 0,
    y: 0,
    isHovering: false,
    isClicking: false,
    cursorType: 'default',
  });

  const prefersReducedMotion =
    typeof window !== 'undefined' && window.matchMedia('(prefers-reduced-motion: reduce)').matches;

  useEffect(() => {
    if (prefersReducedMotion || 'ontouchstart' in window) {
      return;
    }

    const showTimer = setTimeout(() => setIsVisible(true), 100);

    const updateCursor = (e: MouseEvent) => {
      setCursorState(prev => ({
        ...prev,
        x: e.clientX,
        y: e.clientY,
      }));
    };

    const handleMouseDown = () => {
      setCursorState(prev => ({ ...prev, isClicking: true }));
    };

    const handleMouseUp = () => {
      setCursorState(prev => ({ ...prev, isClicking: false }));
    };

    const handleDocumentMouseLeave = () => {
      setIsVisible(false);
    };

    const handleDocumentMouseEnter = () => {
      setIsVisible(true);
    };

    const handleDocumentMouseOver = (e: Event) => {
      const target = e.target as HTMLElement;
      let cursorType: CursorState['cursorType'] = 'default';
      let isHovering = false;

      if (target.matches('button, a, [role="button"]')) {
        cursorType = 'pointer';
        isHovering = true;
      } else if (target.matches('input, textarea, [contenteditable]')) {
        cursorType = 'text';
        isHovering = true;
      } else if (target.matches('.precision-element, .metallic-button, .glass-effect')) {
        cursorType = 'precision';
        isHovering = true;
      } else if (target.matches('.industrial-element, .manufacturing-section')) {
        cursorType = 'industrial';
        isHovering = true;
      }

      setCursorState(prev => ({
        ...prev,
        isHovering,
        cursorType,
      }));
    };

    document.addEventListener('mousemove', updateCursor);
    document.addEventListener('mousedown', handleMouseDown);
    document.addEventListener('mouseup', handleMouseUp);
    document.addEventListener('mouseleave', handleDocumentMouseLeave);
    document.addEventListener('mouseenter', handleDocumentMouseEnter);
    document.addEventListener('mouseover', handleDocumentMouseOver);

    return () => {
      clearTimeout(showTimer);
      document.removeEventListener('mousemove', updateCursor);
      document.removeEventListener('mousedown', handleMouseDown);
      document.removeEventListener('mouseup', handleMouseUp);
      document.removeEventListener('mouseleave', handleDocumentMouseLeave);
      document.removeEventListener('mouseenter', handleDocumentMouseEnter);
      document.removeEventListener('mouseover', handleDocumentMouseOver);
    };
  }, [prefersReducedMotion]);

  useEffect(() => {
    if (cursorRef.current) {
      cursorRef.current.style.transform = `translate(${cursorState.x}px, ${cursorState.y}px)`;
    }
    if (trailRef.current) {
      trailRef.current.style.transform = `translate(${cursorState.x}px, ${cursorState.y}px)`;
    }
  }, [cursorState.x, cursorState.y]);

  const getCursorClasses = () => {
    const baseClasses = 'custom-cursor';
    const stateClasses = [
      cursorState.isHovering && 'hovering',
      cursorState.isClicking && 'clicking',
      `cursor-${cursorState.cursorType}`,
    ]
      .filter(Boolean)
      .join(' ');

    return `${baseClasses} ${stateClasses}`;
  };

  if (prefersReducedMotion || 'ontouchstart' in window || !isVisible) {
    return null;
  }

  return (
    <>
      <div
        ref={trailRef}
        className="custom-cursor-trail"
        style={{
          position: 'fixed',
          top: 0,
          left: 0,
          pointerEvents: 'none',
          zIndex: 9998,
          width: '40px',
          height: '40px',
          borderRadius: '50%',
          background: 'radial-gradient(circle, rgba(121, 22, 255, 0.1) 0%, transparent 70%)',
          transform: `translate(${cursorState.x - 20}px, ${cursorState.y - 20}px)`,
          transition: 'transform 0.15s ease-out',
          opacity: isVisible ? 1 : 0,
        }}
      />
      <div
        ref={cursorRef}
        className={getCursorClasses()}
        style={{
          position: 'fixed',
          top: 0,
          left: 0,
          pointerEvents: 'none',
          zIndex: 9999,
          mixBlendMode: 'difference',
          transform: `translate(${cursorState.x - 10}px, ${cursorState.y - 10}px)`,
          opacity: isVisible ? 1 : 0,
          transition: 'opacity 0.2s ease-out',
        }}
      />
    </>
  );
};

export default CustomCursor;
